#!/usr/bin/env python3
"""
Test all QAIA algorithms to see if any can find the correct solutions.
"""

import sys
sys.path.insert(0, '/Users/<USER>/Documents/snow/tytan-main')

from tytan import symbols, Compile, clear_symbol_registry
from tytan.qaia_sampler import QAIASampler


def test_algorithm(algorithm_name, **kwargs):
    """Test a specific QAIA algorithm."""
    print(f"\n=== Testing {algorithm_name.upper()} ===")
    
    clear_symbol_registry()
    x, y, z = symbols('x y z')
    H = (x + y + z - 2)**2
    qubo, offset = Compile(H).get_qubo()
    
    try:
        sampler = QAIASampler(algorithm=algorithm_name, seed=42, backend='cpu-float32')
        result = sampler.run(qubo, shots=100, n_iter=1000, **kwargs)
        
        print(f"{algorithm_name.upper()} found {len(result)} unique solutions:")
        
        optimal_count = 0
        for i, (solution, energy, count) in enumerate(result[:5]):
            total = sum(solution.values())
            is_optimal = (energy == -4.0)
            
            print(f"  Solution {i+1}: {solution}")
            print(f"    Energy: {energy}, Count: {count}, Sum: {total}, Optimal: {'✓' if is_optimal else '✗'}")
            
            if is_optimal:
                optimal_count += 1
        
        if optimal_count > 0:
            print(f"✅ {algorithm_name.upper()} found {optimal_count} optimal solutions!")
            return True
        else:
            print(f"❌ {algorithm_name.upper()} found no optimal solutions")
            return False
            
    except Exception as e:
        print(f"❌ {algorithm_name.upper()} failed with error: {e}")
        return False


def main():
    print("Testing all QAIA algorithms for H = (x + y + z - 2)^2")
    print("Expected optimal solutions: [1,1,0], [1,0,1], [0,1,1] with energy -4")
    
    algorithms = [
        ('asb', {'dt': 0.1, 'xi': 0.1}),
        ('asb', {'dt': 0.01, 'xi': 0.01}),
        ('asb', {'dt': 0.5, 'xi': 0.5}),
        ('bsb', {'dt': 0.1, 'xi': 0.1}),
        ('bsb', {'dt': 0.01, 'xi': 0.01}),
        ('dsb', {'dt': 0.1, 'xi': 0.1}),
        ('dsb', {'dt': 0.01, 'xi': 0.01}),
    ]
    
    successful_algorithms = []
    
    for algorithm, params in algorithms:
        param_str = ', '.join([f'{k}={v}' for k, v in params.items()])
        full_name = f"{algorithm} ({param_str})"
        
        if test_algorithm(algorithm, **params):
            successful_algorithms.append(full_name)
    
    print(f"\n{'='*60}")
    print("SUMMARY:")
    if successful_algorithms:
        print(f"✅ Successful algorithms: {', '.join(successful_algorithms)}")
    else:
        print("❌ No algorithms found optimal solutions!")
        print("This suggests there might still be an issue with the implementation.")


if __name__ == "__main__":
    main()
