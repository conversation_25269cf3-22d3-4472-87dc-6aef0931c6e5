#!/usr/bin/env python3
"""
Demonstration of QAIASampler with Ising symbols.

This script shows how to use QAIASampler with Ising symbols created using:
symbols('s1 s2', symbol_type="ising")

The sampler automatically handles the conversion from Ising to QUBO format
and back to binary solutions.
"""

from tytan import symbols, Compile, clear_symbol_registry
from tytan.qaia_sampler import QAIASampler


def demo_basic_ising():
    """Basic example with Ising symbols."""
    print("=== Basic Ising Symbols Example ===")
    
    clear_symbol_registry()
    
    # Create Ising symbols as requested
    s1, s2 = symbols('s1 s2', symbol_type="ising")
    print(f"Created Ising symbols: s1, s2 (values ∈ {{-1, +1}})")
    
    # Ising constraint: s1 should equal s2
    expr = (s1 - s2)**2
    print(f"Ising expression: (s1 - s2)^2")
    print("Goal: minimize energy when s1 = s2 (both +1 or both -1)")
    
    # Compile to QUBO (automatic Ising→QUBO conversion)
    qubo, offset = Compile(expr).get_qubo()
    matrix, index_map = qubo
    
    print(f"\nAfter automatic Ising→QUBO conversion:")
    print(f"QUBO matrix:\n{matrix}")
    print(f"Variables: {list(index_map.keys())}")
    print(f"Offset: {offset}")
    
    # Solve with QAIASampler
    sampler = QAIASampler(algorithm='asb', seed=42, backend='cpu-float32')
    result = sampler.run(qubo, shots=20, n_iter=100, dt=0.1, xi=0.1)
    
    print(f"\nQAIASampler results:")
    print(f"Found {len(result)} unique solutions")
    
    # Show best solution
    best_solution = result[0][0]
    best_energy = result[0][1]
    count = result[0][2]
    
    print(f"\nBest solution (binary format): {best_solution}")
    print(f"Energy: {best_energy}")
    print(f"Count: {count}")
    
    # Convert back to Ising interpretation
    s1_ising = 2 * best_solution['s1_bin'] - 1  # 0→-1, 1→+1
    s2_ising = 2 * best_solution['s2_bin'] - 1
    
    print(f"\nIsing interpretation:")
    print(f"s1 = {s1_ising}, s2 = {s2_ising}")
    print(f"Constraint satisfied (s1 = s2): {'✓' if s1_ising == s2_ising else '✗'}")


def demo_ising_chain():
    """Ising chain with multiple spins."""
    print("\n\n=== Ising Chain Example ===")
    
    clear_symbol_registry()
    
    # Create a chain of 4 Ising spins
    s1, s2, s3, s4 = symbols('s1 s2 s3 s4', symbol_type="ising")
    print(f"Created Ising chain: s1-s2-s3-s4 (values ∈ {{-1, +1}})")
    
    # Ferromagnetic Ising model: neighboring spins want to align
    # Energy = -J * Σ(si * si+1) where J > 0 favors alignment
    J = 1.0
    expr = -J * (s1*s2 + s2*s3 + s3*s4)
    
    print(f"Ising expression: -J * (s1*s2 + s2*s3 + s3*s4) with J={J}")
    print("Ferromagnetic model: neighboring spins prefer to align")
    
    # Compile to QUBO
    qubo, offset = Compile(expr).get_qubo()
    matrix, index_map = qubo
    
    print(f"\nQUBO matrix shape: {matrix.shape}")
    print(f"Variables: {list(index_map.keys())}")
    
    # Test different algorithms
    algorithms = ['asb', 'bsb', 'dsb']
    
    for algorithm in algorithms:
        print(f"\n--- Testing {algorithm.upper()} ---")
        
        sampler = QAIASampler(algorithm=algorithm, seed=42, backend='cpu-float32')
        result = sampler.run(qubo, shots=15, n_iter=80, dt=0.1, xi=0.1)
        
        best_solution = result[0][0]
        best_energy = result[0][1]
        
        print(f"Best solution: {best_solution}")
        print(f"Energy: {best_energy}")
        
        # Convert to Ising values
        ising_values = []
        for i in range(1, 5):
            var_name = f's{i}_bin'
            if var_name in best_solution:
                ising_val = 2 * best_solution[var_name] - 1
                ising_values.append(ising_val)
        
        print(f"Ising chain: {' - '.join(map(str, ising_values))}")
        
        # Check alignment
        transitions = sum(1 for i in range(len(ising_values)-1) 
                         if ising_values[i] != ising_values[i+1])
        print(f"Spin transitions: {transitions} (fewer is better for ferromagnetic)")


def demo_ising_antiferromagnetic():
    """Antiferromagnetic Ising model."""
    print("\n\n=== Antiferromagnetic Ising Model ===")
    
    clear_symbol_registry()
    
    # Create Ising spins
    s1, s2, s3, s4 = symbols('s1 s2 s3 s4', symbol_type="ising")
    print(f"Created Ising spins for antiferromagnetic model")
    
    # Antiferromagnetic: neighboring spins want to be opposite
    # Energy = +J * Σ(si * si+1) where J > 0 favors anti-alignment
    J = 1.0
    expr = J * (s1*s2 + s2*s3 + s3*s4 + s4*s1)  # Ring topology
    
    print(f"Ising expression: +J * (s1*s2 + s2*s3 + s3*s4 + s4*s1) with J={J}")
    print("Antiferromagnetic ring: neighboring spins prefer opposite values")
    
    # Compile to QUBO
    qubo, offset = Compile(expr).get_qubo()
    
    # Solve with DSB (good for discrete problems)
    sampler = QAIASampler(algorithm='dsb', seed=42, backend='cpu-float32')
    result = sampler.run(qubo, shots=20, n_iter=100, dt=0.1, xi=0.1)
    
    best_solution = result[0][0]
    best_energy = result[0][1]
    
    print(f"\nBest solution: {best_solution}")
    print(f"Energy: {best_energy}")
    
    # Convert to Ising and visualize ring
    ising_ring = []
    for i in range(1, 5):
        var_name = f's{i}_bin'
        if var_name in best_solution:
            ising_val = 2 * best_solution[var_name] - 1
            ising_ring.append(ising_val)
    
    print(f"\nIsing ring configuration:")
    print(f"s1={ising_ring[0]} - s2={ising_ring[1]}")
    print(f" |                    |")
    print(f"s4={ising_ring[3]} - s3={ising_ring[2]}")
    
    # Check antiferromagnetic satisfaction
    pairs = [(0,1), (1,2), (2,3), (3,0)]  # Ring connections
    satisfied_pairs = 0
    for i, j in pairs:
        if ising_ring[i] != ising_ring[j]:  # Opposite spins
            satisfied_pairs += 1
    
    print(f"\nAntiferromagnetic pairs satisfied: {satisfied_pairs}/4")


def demo_ising_with_field():
    """Ising model with external magnetic field."""
    print("\n\n=== Ising Model with External Field ===")
    
    clear_symbol_registry()
    
    # Create Ising spins
    s1, s2, s3 = symbols('s1 s2 s3', symbol_type="ising")
    print(f"Created Ising spins with external field")
    
    # Ising model with external field favoring +1 state
    # Energy = -J * (s1*s2 + s2*s3) - h * (s1 + s2 + s3)
    J = 0.5  # Coupling strength
    h = 1.0  # External field strength
    
    expr = -J * (s1*s2 + s2*s3) - h * (s1 + s2 + s3)
    
    print(f"Ising expression: -J*(s1*s2 + s2*s3) - h*(s1 + s2 + s3)")
    print(f"J={J} (coupling), h={h} (external field)")
    print("External field favors all spins = +1")
    
    # Compile to QUBO
    qubo, offset = Compile(expr).get_qubo()
    
    # Solve with ASB
    sampler = QAIASampler(algorithm='asb', seed=42, backend='cpu-float32')
    result = sampler.run(qubo, shots=15, n_iter=100, dt=0.1, xi=0.1)
    
    best_solution = result[0][0]
    best_energy = result[0][1]
    
    print(f"\nBest solution: {best_solution}")
    print(f"Energy: {best_energy}")
    
    # Convert to Ising
    ising_values = {}
    for i in range(1, 4):
        var_name = f's{i}_bin'
        if var_name in best_solution:
            ising_val = 2 * best_solution[var_name] - 1
            ising_values[f's{i}'] = ising_val
    
    print(f"Ising configuration: {ising_values}")
    
    # Check if field effect is visible
    positive_spins = sum(1 for val in ising_values.values() if val == 1)
    print(f"Spins pointing up (+1): {positive_spins}/3")
    print(f"Field effect: {'✓' if positive_spins >= 2 else '✗'} (field favors +1)")


def main():
    """Run all Ising symbol demonstrations."""
    print("🧲 QAIASampler with Ising Symbols Demonstration")
    print("=" * 60)
    print("Using symbols('s1 s2', symbol_type=\"ising\")")
    print("=" * 60)
    
    try:
        demo_basic_ising()
        demo_ising_chain()
        demo_ising_antiferromagnetic()
        demo_ising_with_field()
        
        print("\n" + "=" * 60)
        print("✅ All Ising symbol demonstrations completed!")
        print("\nKey observations:")
        print("1. Ising symbols are automatically converted to QUBO format")
        print("2. Variable names get '_bin' suffix after conversion")
        print("3. Solutions are returned in binary format (0,1)")
        print("4. Convert back to Ising: ising_value = 2 * binary_value - 1")
        print("5. All QAIA algorithms (ASB, BSB, DSB) work with Ising symbols")
        print("6. QAIASampler maintains mathematical correctness")
        
    except Exception as e:
        print(f"\n❌ Error during demonstration: {e}")
        print("Make sure the conda environment is properly activated.")


if __name__ == "__main__":
    main()
