#!/usr/bin/env python3
"""
Test the bug fix for QAIASampler with the specific case:
H = (x + y + z - 2)**2
"""

from tytan import symbols, Compile, clear_symbol_registry
from tytan.qaia_sampler import QAIASampler
import numpy as np


def test_bug_fix():
    """Test the specific bug case."""
    print("Testing QAIASampler bug fix...")
    print("Problem: H = (x + y + z - 2)^2")
    print("Expected: exactly 2 out of 3 variables should be 1")
    
    clear_symbol_registry()
    
    # Create the exact test case
    x = symbols('x')
    y = symbols('y')
    z = symbols('z')
    H = (x + y + z - 2)**2
    
    # Compile to QUBO
    qubo, offset = Compile(H).get_qubo()
    matrix, index_map = qubo
    
    print(f"\nQUBO matrix:\n{matrix}")
    print(f"Index map: {index_map}")
    print(f"Offset: {offset}")
    
    # Expected optimal solutions (sum = 2)
    expected_solutions = [
        [1, 1, 0],  # x=1, y=1, z=0
        [1, 0, 1],  # x=1, y=0, z=1
        [0, 1, 1],  # x=0, y=1, z=1
    ]
    
    print(f"\nExpected optimal solutions:")
    for i, sol in enumerate(expected_solutions):
        sol_array = np.array(sol)
        energy = sol_array.T @ matrix @ sol_array
        constraint_sum = sum(sol)
        print(f"  Solution {i+1}: {sol} -> QUBO energy={energy}, sum={constraint_sum}")
    
    # Test QAIASampler
    print(f"\nTesting QAIASampler with fixed conversion...")
    
    try:
        sampler = QAIASampler(algorithm='asb', seed=42, backend='cpu-float32')
        result = sampler.run(qubo, shots=20, n_iter=100, dt=0.1, xi=0.1)
        
        print(f"QAIASampler found {len(result)} unique solutions:")
        
        correct_solutions = 0
        for i, (solution, energy, count) in enumerate(result[:5]):  # Show top 5
            total = sum(solution.values())
            is_optimal = (total == 2 and abs(energy - (-4.0)) < 1e-6)
            
            print(f"  Solution {i+1}: {solution}")
            print(f"    Energy: {energy}, Count: {count}, Sum: {total}")
            print(f"    Optimal: {'✓' if is_optimal else '✗'}")
            
            if is_optimal:
                correct_solutions += 1
        
        print(f"\nSummary:")
        print(f"  Correct optimal solutions found: {correct_solutions}")
        print(f"  Total solutions found: {len(result)}")
        
        if correct_solutions > 0:
            print("✅ BUG FIX SUCCESSFUL: QAIASampler found correct solutions!")
        else:
            print("❌ BUG STILL EXISTS: No correct solutions found")
            
        return correct_solutions > 0
        
    except Exception as e:
        print(f"❌ Error running QAIASampler: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_conversion_manually():
    """Test the conversion logic manually."""
    print("\n" + "="*50)
    print("Testing QUBO to Ising conversion manually...")
    
    clear_symbol_registry()
    x = symbols('x')
    y = symbols('y')
    z = symbols('z')
    H = (x + y + z - 2)**2
    
    qubo, offset = Compile(H).get_qubo()
    matrix, index_map = qubo
    
    # Test the conversion
    sampler = QAIASampler(algorithm='asb', backend='cpu-float32')
    J, h, ising_index_map, ising_offset = sampler._qubo_to_ising(matrix, index_map)
    
    print(f"Original QUBO matrix:\n{matrix}")
    print(f"Converted J matrix:\n{J}")
    print(f"Converted h vector: {h}")
    print(f"Converted offset: {ising_offset}")
    
    # Test with expected solutions
    expected_binary_solutions = [
        np.array([1, 1, 0]),  # x=1, y=1, z=0
        np.array([1, 0, 1]),  # x=1, y=0, z=1
        np.array([0, 1, 1]),  # x=0, y=1, z=1
    ]
    
    print(f"\nVerifying energy equivalence:")
    all_match = True
    
    for i, binary_sol in enumerate(expected_binary_solutions):
        # QUBO energy
        qubo_energy = binary_sol.T @ matrix @ binary_sol
        
        # Convert to Ising
        ising_sol = 2 * binary_sol - 1
        
        # Ising energy
        ising_energy = ising_sol.T @ J @ ising_sol + h.T @ ising_sol + ising_offset
        
        match = abs(qubo_energy - ising_energy) < 1e-10
        all_match = all_match and match
        
        print(f"  Solution {i+1}: {binary_sol}")
        print(f"    QUBO energy: {qubo_energy}")
        print(f"    Ising energy: {ising_energy}")
        print(f"    Match: {'✓' if match else '✗'}")
    
    if all_match:
        print("✅ CONVERSION CORRECT: All energies match!")
    else:
        print("❌ CONVERSION ERROR: Energy mismatch detected")
    
    return all_match


if __name__ == "__main__":
    print("🐛 QAIASampler Bug Fix Test")
    print("="*60)
    
    # Test conversion logic
    conversion_ok = test_conversion_manually()
    
    # Test actual sampler
    sampler_ok = test_bug_fix()
    
    print("\n" + "="*60)
    print("FINAL RESULTS:")
    print(f"  Conversion logic: {'✅ FIXED' if conversion_ok else '❌ BROKEN'}")
    print(f"  Sampler results: {'✅ FIXED' if sampler_ok else '❌ BROKEN'}")
    
    if conversion_ok and sampler_ok:
        print("\n🎉 BUG COMPLETELY FIXED!")
    elif conversion_ok:
        print("\n⚠️  Conversion fixed but sampler may need parameter tuning")
    else:
        print("\n❌ Bug still exists - conversion logic needs more work")
