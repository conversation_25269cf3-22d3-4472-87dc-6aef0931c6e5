import unittest

import numpy as np
from tytan import (Auto_array, Compile, sampler, symbols, symbols_define,
                   symbols_list, symbols_nbit)
from tytan.compile import calc_degree
import symengine
import pytest


def are_same_qubo_matrices(qubo1, qubo2):
    
    qubo1 = np.array(qubo1, float)
    qubo2 = np.array(qubo2, float)
    
    eps = 1e-9
    
    if np.sum(qubo1 - qubo2) < eps:
        return True
    else:
        return False


class TestCompile(unittest.TestCase):
    def test_maxcut1(self):
        #量子ビットを用意する
        q0 = symbols('q0')
        q1 = symbols('q1')
        q2 = symbols('q2')

        #3個の量子ビットから2個を1にする
        H = (q0 + q1 + q2 - 2)**2

        #コンパイル
        qubo, offset = Compile(H).get_qubo()
        expected_qubo_matrix = \
            [[-3.0, 2.0, 2.0],\
             [0.0, -3.0, 2.0],\
             [0.0, 0.0, -3.0]]
        self.assertTrue(are_same_qubo_matrices(qubo[0], expected_qubo_matrix))
        self.assertEqual(offset, 4)

    def test_maxcut2(self):
        #量子ビットを用意する
        q0 = symbols('q0')
        q1 = symbols('q1')
        q2 = symbols('q2')
        q3 = symbols('q3')
        q4 = symbols('q4')

        #友達関係において、違うバスに乗せたい（＝2個の量子ビットを0,1逆にしたい）（＝2個の量子ビットから1個を1にしたい）
        H = 0
        H += (q0 + q1 - 1)**2
        H += (q0 + q2 - 1)**2
        H += (q1 + q3 - 1)**2
        H += (q2 + q3 - 1)**2
        H += (q2 + q4 - 1)**2
        H += (q3 + q4 - 1)**2

        #コンパイル
        qubo, offset = Compile(H).get_qubo()
        expected_qubo_matrix = \
            [[-2.0, 2.0, 2.0, 0.0, 0.0],\
             [0.0, -2.0, 0.0, 2.0, 0.0],\
             [0.0, 0.0, -3.0, 2.0, 2.0],\
             [0.0, 0.0, 0.0, -3.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, -2.0]]
        self.assertTrue(are_same_qubo_matrices(qubo[0], expected_qubo_matrix))
        self.assertEqual(offset, 6)

    def test_thermometer1(self):
        #量子ビットを用意する
        q00 = symbols('q00')
        q01 = symbols('q01')
        q02 = symbols('q02')
        q03 = symbols('q03')
        q04 = symbols('q04')
        q05 = symbols('q05')
        q06 = symbols('q06')
        q07 = symbols('q07')
        q08 = symbols('q08')
        q09 = symbols('q09')
        q10 = symbols('q10')
        q11 = symbols('q11')
        q12 = symbols('q12')
        q13 = symbols('q13')
        q14 = symbols('q14')
        q15 = symbols('q15')

        #各行、「4個から指定の個数だけ1になる」
        H = 0
        H += (q00 + q01 + q02 + q03 - 2)**2
        H += (q04 + q05 + q06 + q07 - 1)**2
        H += (q08 + q09 + q10 + q11 - 3)**2
        H += (q12 + q13 + q14 + q15 - 1)**2

        #各列、「4個から指定の個数だけ1になる」
        H += (q00 + q04 + q08 + q12 - 3)**2
        H += (q01 + q05 + q09 + q13 - 1)**2
        H += (q02 + q06 + q10 + q14 - 1)**2
        H += (q03 + q07 + q11 + q15 - 2)**2

        #各温度計、球部から降順になる
        H += (1 - q08) * q04
        H += (1 - q04) * q00 #8→4→0の連鎖

        H += (1 - q05) * q01

        H += (1 - q03) * q02

        H += (1 - q07) * q06

        H += (1 - q11) * q10
        H += (1 - q10) * q09 #11→10→9の連鎖

        H += (1 - q13) * q12

        H += (1 - q15) * q14

        #コンパイル
        qubo, offset = Compile(H).get_qubo()
        expected_qubo_matrix = \
            [[-7.0, 2.0, 2.0, 2.0, 1.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 2.0, 0.0,\
              0.0, 0.0],\
             [0.0, -3.0, 2.0, 2.0, 0.0, 1.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 2.0,\
              0.0, 0.0],\
             [0.0, 0.0, -3.0, 1.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0,\
              2.0, 0.0],\
             [0.0, 0.0, 0.0, -6.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0,\
              0.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, -5.0, 2.0, 2.0, 2.0, 1.0, 0.0, 0.0, 0.0, 2.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, -2.0, 2.0, 2.0, 0.0, 2.0, 0.0, 0.0, 0.0, 2.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 1.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0,\
              2.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -4.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0,\
              0.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -10.0, 2.0, 2.0, 2.0, 2.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -5.0, 1.0, 2.0, 0.0, 2.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -5.0, 1.0, 0.0, 0.0,\
              2.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -8.0, 0.0, 0.0,\
              0.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -5.0, 1.0,\
              2.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -2.0,\
              2.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              -1.0, 1.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, -4.0]]
        self.assertTrue(are_same_qubo_matrices(qubo[0], expected_qubo_matrix))
        self.assertEqual(offset, 30)

    def test_thermometer2(self):
        #量子ビットを用意する
        q = symbols_list([4, 4], 'q{}_{}')

        #各行、「4個から指定の個数だけ1になる」
        H = 0
        H += (q[0][0] + q[0][1] + q[0][2] + q[0][3] - 2)**2
        H += (q[1][0] + q[1][1] + q[1][2] + q[1][3] - 1)**2
        H += (q[2][0] + q[2][1] + q[2][2] + q[2][3] - 3)**2
        H += (q[3][0] + q[3][1] + q[3][2] + q[3][3] - 1)**2

        #各列、「4個から指定の個数だけ1になる」
        H += (q[0][0] + q[1][0] + q[2][0] + q[3][0] - 3)**2
        H += (q[0][1] + q[1][1] + q[2][1] + q[3][1] - 1)**2
        H += (q[0][2] + q[1][2] + q[2][2] + q[3][2] - 1)**2
        H += (q[0][3] + q[1][3] + q[2][3] + q[3][3] - 2)**2

        #各温度計、球部から降順になる
        H += (1 - q[2][0]) * q[1][0]
        H += (1 - q[1][0]) * q[0][0] #8→4→0の連鎖

        H += (1 - q[1][1]) * q[0][1]

        H += (1 - q[0][3]) * q[0][2]

        H += (1 - q[1][3]) * q[1][2]

        H += (1 - q[2][3]) * q[2][2]
        H += (1 - q[2][2]) * q[2][1] #11→10→9の連鎖

        H += (1 - q[3][1]) * q[3][0]

        H += (1 - q[3][3]) * q[3][2]

        #コンパイル
        qubo, offset = Compile(H).get_qubo()
        expected_qubo_matrix = \
            [[-7.0, 2.0, 2.0, 2.0, 1.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 2.0, 0.0,\
              0.0, 0.0],\
             [0.0, -3.0, 2.0, 2.0, 0.0, 1.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 2.0,\
              0.0, 0.0],\
             [0.0, 0.0, -3.0, 1.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0,\
              2.0, 0.0],\
             [0.0, 0.0, 0.0, -6.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0,\
              0.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, -5.0, 2.0, 2.0, 2.0, 1.0, 0.0, 0.0, 0.0, 2.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, -2.0, 2.0, 2.0, 0.0, 2.0, 0.0, 0.0, 0.0, 2.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 1.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0,\
              2.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -4.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0,\
              0.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -10.0, 2.0, 2.0, 2.0, 2.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -5.0, 1.0, 2.0, 0.0, 2.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -5.0, 1.0, 0.0, 0.0,\
              2.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -8.0, 0.0, 0.0,\
              0.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -5.0, 1.0,\
              2.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -2.0,\
              2.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              -1.0, 1.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, -4.0]]
        self.assertTrue(are_same_qubo_matrices(qubo[0], expected_qubo_matrix))
        self.assertEqual(offset, 30)

    def test_balanced_numbers1(self):
        #量子ビットを用意する
        q0 = symbols('q0')
        q1 = symbols('q1')
        q2 = symbols('q2')
        q3 = symbols('q3')
        q4 = symbols('q4')
        q5 = symbols('q5')

        #グループA（＝1になった量子ビット）は合計130
        H = 0
        H += (15*q0 + 25*q1 + 33*q2 + 41*q3 + 64*q4 + 82*q5 - 130)**2

        #グループB（＝0になった量子ビット）は合計130
        H += (15*(1-q0) + 25*(1-q1) + 33*(1-q2) + 41*(1-q3) + 64*(1-q4) + 82*(1-q5) - 130)**2


        #コンパイル
        qubo, offset = Compile(H).get_qubo()
        expected_qubo_matrix = \
            [[-7350.0, 1500.0, 1980.0, 2460.0, 3840.0, 4920.0],\
             [0.0, -11750.0, 3300.0, 4100.0, 6400.0, 8200.0],\
             [0.0, 0.0, -14982.0, 5412.0, 8448.0, 10824.0],\
             [0.0, 0.0, 0.0, -17958.0, 10496.0, 13448.0],\
             [0.0, 0.0, 0.0, 0.0, -25088.0, 20992.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, -29192.0]]
        self.assertTrue(are_same_qubo_matrices(qubo[0], expected_qubo_matrix))
        self.assertEqual(offset, 33800)

    def test_balanced_numbers2(self):
        #数字
        numbers = np.array([15, 25, 33, 41, 64, 82])

        #QUBO変数を用意する→[0, 1]の変数
        q = symbols_list(6, 'q{}')

        #まとめてイジング変数に変換→[-1, +1]の変数
        z = 2*q - 1

        # イジング変数は-1か+1である。イジング変数配列と数字配列の内積が0になればよい
        # H = (15*z0 + 25*z1 + 33*z2 + 41*z3 + 64*z4 + 82*z5 - 0)**2　ということ
        H = sum(numbers * z)**2

        #コンパイル
        qubo, offset = Compile(H).get_qubo()
        expected_qubo_matrix = \
            [[-14700.0, 3000.0, 3960.0, 4920.0, 7680.0, 9840.0],\
             [0.0, -23500.0, 6600.0, 8200.0, 12800.0, 16400.0],\
             [0.0, 0.0, -29964.0, 10824.0, 16896.0, 21648.0],\
             [0.0, 0.0, 0.0, -35916.0, 20992.0, 26896.0],\
             [0.0, 0.0, 0.0, 0.0, -50176.0, 41984.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, -58384.0]]
        self.assertTrue(are_same_qubo_matrices(qubo[0], expected_qubo_matrix))
        self.assertEqual(offset, 67600)

    def test_schedule_optimization(self):
        #量子ビットを用意する
        q0 = symbols('q0')
        q1 = symbols('q1')
        q2 = symbols('q2')
        q3 = symbols('q3')
        q4 = symbols('q4')
        q5 = symbols('q5')
        q6 = symbols('q6')
        q7 = symbols('q7')

        #各時間帯とも、パワー合計が2になる
        H = 0
        H += (q0 + q1 + 2*q5 - 2)**2
        H += (q2 + 2*q6 - 2)**2
        H += (q3 + 2*q7 - 2)**2
        H += (q1 + q3 + q4 - 2)**2
        H += (q0 + q2 + q4 - 2)**2


        #コンパイル
        qubo, offset = Compile(H).get_qubo()
        expected_qubo_matrix = \
            [[-6.0, 2.0, 2.0, 0.0, 2.0, 4.0, 0.0, 0.0],\
             [0.0, -6.0, 0.0, 2.0, 2.0, 4.0, 0.0, 0.0],\
             [0.0, 0.0, -6.0, 0.0, 2.0, 0.0, 4.0, 0.0],\
             [0.0, 0.0, 0.0, -6.0, 2.0, 0.0, 0.0, 4.0],\
             [0.0, 0.0, 0.0, 0.0, -6.0, 0.0, 0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, -4.0, 0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -4.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -4.0]]
        self.assertTrue(are_same_qubo_matrices(qubo[0], expected_qubo_matrix))
        self.assertEqual(offset, 20)

    def test_drawings1(self):
        #量子ビットを用意する
        q00 = symbols('q00')
        q01 = symbols('q01')
        q02 = symbols('q02')
        q03 = symbols('q03')
        q04 = symbols('q04')
        q05 = symbols('q05')
        q06 = symbols('q06')
        q07 = symbols('q07')
        q08 = symbols('q08')
        q09 = symbols('q09')
        q10 = symbols('q10')
        q11 = symbols('q11')
        q12 = symbols('q12')
        q13 = symbols('q13')
        q14 = symbols('q14')
        q15 = symbols('q15')
        q16 = symbols('q16')
        q17 = symbols('q17')
        q18 = symbols('q18')
        q19 = symbols('q19')
        q20 = symbols('q20')
        q21 = symbols('q21')
        q22 = symbols('q22')
        q23 = symbols('q23')
        q24 = symbols('q24')

        #各行、個数の制約
        H = 0
        H += (q00 + q01 + q02 + q03 + q04 - 2)**2
        H += (q05 + q06 + q07 + q08 + q09 - 3)**2
        H += (q10 + q11 + q12 + q13 + q14 - 3)**2
        H += (q15 + q16 + q17 + q18 + q19 - 3)**2
        H += (q20 + q21 + q22 + q23 + q24 - 2)**2

        #各列、個数の制約
        H += (q00 + q05 + q10 + q15 + q20 - 3)**2
        H += (q01 + q06 + q11 + q16 + q21 - 2)**2
        H += (q02 + q07 + q12 + q17 + q22 - 5)**2
        H += (q03 + q08 + q13 + q18 + q23 - 2)**2
        H += (q04 + q09 + q14 + q19 + q24 - 1)**2

        #各行、連続の報酬
        H += -0.1 * (q00 * q01) -0.1 * (q01 * q02) -0.1 * (q02 * q03) -0.1 * (q03 * q04)
        H += -0.1 * (q05 * q06) -0.1 * (q06 * q07) -0.1 * (q07 * q08) -0.1 * (q08 * q09)
        H += -0.1 * (q10 * q11) -0.1 * (q11 * q12) -0.1 * (q12 * q13) -0.1 * (q13 * q14)
        H += -0.1 * (q15 * q16) -0.1 * (q16 * q17) -0.1 * (q17 * q18) -0.1 * (q18 * q19)
        #[1, 1]スプリットは後ほど設定

        #各列、連続の報酬
        H += -0.1 * (q00 * q05) -0.1 * (q05 * q10) -0.1 * (q10 * q15) -0.1 * (q15 * q20)
        H += -0.1 * (q01 * q06) -0.1 * (q06 * q11) -0.1 * (q11 * q16) -0.1 * (q16 * q21)
        H += -0.1 * (q02 * q07) -0.1 * (q07 * q12) -0.1 * (q12 * q17) -0.1 * (q17 * q22)
        H += -0.1 * (q03 * q08) -0.1 * (q08 * q13) -0.1 * (q13 * q18) -0.1 * (q18 * q23)
        #[1]の列は連続設定は不要

        #[1, 1]スプリット列のペナルティ
        H += 0.1 * (q20 * q21) + 0.1 * (q21 * q22) + 0.1 * (q22 * q23) + 0.1 * (q23 * q24)


        #コンパイル
        qubo, offset = Compile(H).get_qubo()
        expected_qubo_matrix = \
            [[-8.0, 1.9, 2.0, 2.0, 2.0, 1.9, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0,\
              0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0],\
             [0.0, -6.0, 1.9, 2.0, 2.0, 0.0, 1.9, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0,\
              0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0],\
             [0.0, 0.0, -12.0, 1.9, 2.0, 0.0, 0.0, 1.9, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0,\
              0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0],\
             [0.0, 0.0, 0.0, -6.0, 1.9, 0.0, 0.0, 0.0, 1.9, 0.0, 0.0, 0.0, 0.0, 2.0,\
              0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, -4.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0,\
              2.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, -10.0, 1.9, 2.0, 2.0, 2.0, 1.9, 0.0, 0.0, 0.0,\
              0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -8.0, 1.9, 2.0, 2.0, 0.0, 1.9, 0.0, 0.0,\
              0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -14.0, 1.9, 2.0, 0.0, 0.0, 1.9, 0.0,\
              0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -8.0, 1.9, 0.0, 0.0, 0.0, 1.9,\
              0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -6.0, 0.0, 0.0, 0.0, 0.0,\
              2.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -10.0, 1.9, 2.0, 2.0,\
              2.0, 1.9, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -8.0, 1.9, 2.0,\
              2.0, 0.0, 1.9, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -14.0, 1.9,\
              2.0, 0.0, 0.0, 1.9, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -8.0,\
              1.9, 0.0, 0.0, 0.0, 1.9, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              -6.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, -10.0, 1.9, 2.0, 2.0, 2.0, 1.9, 0.0, 0.0, 0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, -8.0, 1.9, 2.0, 2.0, 0.0, 1.9, 0.0, 0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, -14.0, 1.9, 2.0, 0.0, 0.0, 1.9, 0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, -8.0, 1.9, 0.0, 0.0, 0.0, 1.9, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, -6.0, 0.0, 0.0, 0.0, 0.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -8.0, 2.1, 2.0, 2.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -6.0, 2.1, 2.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -12.0, 2.1, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -6.0, 2.1],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -4.0]]
        self.assertTrue(are_same_qubo_matrices(qubo[0], expected_qubo_matrix))
        self.assertEqual(offset, 78)

    def test_drawings2(self):
        #量子ビットを用意する
        q = symbols_list([5, 5], 'q{}_{}')

        #各行、個数の制約
        H = 0
        H += (q[0][0] + q[0][1] + q[0][2] + q[0][3] + q[0][4] - 2)**2
        H += (q[1][0] + q[1][1] + q[1][2] + q[1][3] + q[1][4] - 3)**2
        H += (q[2][0] + q[2][1] + q[2][2] + q[2][3] + q[2][4] - 3)**2
        H += (q[3][0] + q[3][1] + q[3][2] + q[3][3] + q[3][4] - 3)**2
        H += (q[4][0] + q[4][1] + q[4][2] + q[4][3] + q[4][4] - 2)**2

        #各列、個数の制約
        H += (q[0][0] + q[1][0] + q[2][0] + q[3][0] + q[4][0] - 3)**2
        H += (q[0][1] + q[1][1] + q[2][1] + q[3][1] + q[4][1] - 2)**2
        H += (q[0][2] + q[1][2] + q[2][2] + q[3][2] + q[4][2] - 5)**2
        H += (q[0][3] + q[1][3] + q[2][3] + q[3][3] + q[4][3] - 2)**2
        H += (q[0][4] + q[1][4] + q[2][4] + q[3][4] + q[4][4] - 1)**2

        #各行、連続の報酬
        H += -0.1 * (q[0][0] * q[0][1]) -0.1 * (q[0][1] * q[0][2]) -0.1 * (q[0][2] * q[0][3]) -0.1 * (q[0][3] * q[0][4])
        H += -0.1 * (q[1][0] * q[1][1]) -0.1 * (q[1][1] * q[1][2]) -0.1 * (q[1][2] * q[1][3]) -0.1 * (q[1][3] * q[1][4])
        H += -0.1 * (q[2][0] * q[2][1]) -0.1 * (q[2][1] * q[2][2]) -0.1 * (q[2][2] * q[2][3]) -0.1 * (q[2][3] * q[2][4])
        H += -0.1 * (q[3][0] * q[3][1]) -0.1 * (q[3][1] * q[3][2]) -0.1 * (q[3][2] * q[3][3]) -0.1 * (q[3][3] * q[3][4])
        #[1, 1]スプリットは後ほど設定

        #各列、連続の報酬
        H += -0.1 * (q[0][0] * q[1][0]) -0.1 * (q[1][0] * q[2][0]) -0.1 * (q[2][0] * q[3][0]) -0.1 * (q[3][0] * q[4][0])
        H += -0.1 * (q[0][1] * q[1][1]) -0.1 * (q[1][1] * q[2][1]) -0.1 * (q[2][1] * q[3][1]) -0.1 * (q[3][1] * q[4][1])
        H += -0.1 * (q[0][2] * q[1][2]) -0.1 * (q[1][2] * q[2][2]) -0.1 * (q[2][2] * q[3][2]) -0.1 * (q[3][2] * q[4][2])
        H += -0.1 * (q[0][3] * q[1][3]) -0.1 * (q[1][3] * q[2][3]) -0.1 * (q[2][3] * q[3][3]) -0.1 * (q[3][3] * q[4][3])
        #[1]の列は連続設定は不要

        #[1, 1]スプリット列のペナルティ
        H += 0.1 * (q[4][0] * q[4][1]) + 0.1 * (q[4][1] * q[4][2]) + 0.1 * (q[4][2] * q[4][3]) + 0.1 * (q[4][3] * q[4][4])


        #コンパイル
        qubo, offset = Compile(H).get_qubo()
        expected_qubo_matrix = \
            [[-8.0, 1.9, 2.0, 2.0, 2.0, 1.9, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0,\
              0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0],\
             [0.0, -6.0, 1.9, 2.0, 2.0, 0.0, 1.9, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0,\
              0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0],\
             [0.0, 0.0, -12.0, 1.9, 2.0, 0.0, 0.0, 1.9, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0,\
              0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0],\
             [0.0, 0.0, 0.0, -6.0, 1.9, 0.0, 0.0, 0.0, 1.9, 0.0, 0.0, 0.0, 0.0, 2.0,\
              0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, -4.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0,\
              2.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, -10.0, 1.9, 2.0, 2.0, 2.0, 1.9, 0.0, 0.0, 0.0,\
              0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -8.0, 1.9, 2.0, 2.0, 0.0, 1.9, 0.0, 0.0,\
              0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -14.0, 1.9, 2.0, 0.0, 0.0, 1.9, 0.0,\
              0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -8.0, 1.9, 0.0, 0.0, 0.0, 1.9,\
              0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -6.0, 0.0, 0.0, 0.0, 0.0,\
              2.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -10.0, 1.9, 2.0, 2.0,\
              2.0, 1.9, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -8.0, 1.9, 2.0,\
              2.0, 0.0, 1.9, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -14.0, 1.9,\
              2.0, 0.0, 0.0, 1.9, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -8.0,\
              1.9, 0.0, 0.0, 0.0, 1.9, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              -6.0, 0.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0, 0.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, -10.0, 1.9, 2.0, 2.0, 2.0, 1.9, 0.0, 0.0, 0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, -8.0, 1.9, 2.0, 2.0, 0.0, 1.9, 0.0, 0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, -14.0, 1.9, 2.0, 0.0, 0.0, 1.9, 0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, -8.0, 1.9, 0.0, 0.0, 0.0, 1.9, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, -6.0, 0.0, 0.0, 0.0, 0.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -8.0, 2.1, 2.0, 2.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -6.0, 2.1, 2.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -12.0, 2.1, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -6.0, 2.1],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -4.0]]
        self.assertTrue(are_same_qubo_matrices(qubo[0], expected_qubo_matrix))
        self.assertEqual(offset, 78)

    def test_tsp(self):
        #量子ビットを用意する
        q00 = symbols('q00')
        q01 = symbols('q01')
        q02 = symbols('q02')
        q03 = symbols('q03')
        q04 = symbols('q04')
        q05 = symbols('q05')
        q06 = symbols('q06')
        q07 = symbols('q07')
        q08 = symbols('q08')
        q09 = symbols('q09')
        q10 = symbols('q10')
        q11 = symbols('q11')
        q12 = symbols('q12')
        q13 = symbols('q13')
        q14 = symbols('q14')
        q15 = symbols('q15')

        #1番目に訪れる都市は1つだけにしたい
        H = 0
        H += (q00 + q01 + q02 + q03 - 1)**2
        H += (q04 + q05 + q06 + q07 - 1)**2
        H += (q08 + q09 + q10 + q11 - 1)**2
        H += (q12 + q13 + q14 + q15 - 1)**2

        #都市Aに訪れる順番は1つだけにしたい
        H += (q00 + q04 + q08 + q12 - 1)**2
        H += (q01 + q05 + q09 + q13 - 1)**2
        H += (q02 + q06 + q10 + q14 - 1)**2
        H += (q03 + q07 + q11 + q15 - 1)**2

        #都市間の距離に比例したペナルティ
        #1番目から2番目への移動について
        H += 0.0 * (q00 * q04) #距離0なので
        H += 0.3 * (q01 * q04) #距離3なので
        H += 0.2 * (q02 * q04) #距離2なので
        H += 0.6 * (q03 * q04) #距離6なので

        H += 0.3 * (q00 * q05)
        H += 0.0 * (q01 * q05)
        H += 0.1 * (q02 * q05)
        H += 0.2 * (q03 * q05)

        H += 0.2 * (q00 * q06)
        H += 0.1 * (q01 * q06)
        H += 0.0 * (q02 * q06)
        H += 0.3 * (q03 * q06)

        H += 0.6 * (q00 * q07)
        H += 0.2 * (q01 * q07)
        H += 0.3 * (q02 * q07)
        H += 0.0 * (q03 * q07)

        #2番目から3番目への移動について
        H += 0.0 * (q04 * q08)
        H += 0.3 * (q05 * q08)
        H += 0.2 * (q06 * q08)
        H += 0.6 * (q07 * q08)

        H += 0.3 * (q04 * q09)
        H += 0.0 * (q05 * q09)
        H += 0.1 * (q06 * q09)
        H += 0.2 * (q07 * q09)

        H += 0.2 * (q04 * q10)
        H += 0.1 * (q05 * q10)
        H += 0.0 * (q06 * q10)
        H += 0.3 * (q07 * q10)

        H += 0.6 * (q04 * q11)
        H += 0.2 * (q05 * q11)
        H += 0.3 * (q06 * q11)
        H += 0.0 * (q07 * q11)

        #3番目から4番目への移動について
        H += 0.0 * (q08 * q12)
        H += 0.3 * (q09 * q12)
        H += 0.2 * (q10 * q12)
        H += 0.6 * (q11 * q12)

        H += 0.3 * (q08 * q13)
        H += 0.0 * (q09 * q13)
        H += 0.1 * (q10 * q13)
        H += 0.2 * (q11 * q13)

        H += 0.2 * (q08 * q14)
        H += 0.1 * (q09 * q14)
        H += 0.0 * (q10 * q14)
        H += 0.3 * (q11 * q14)

        H += 0.6 * (q08 * q15)
        H += 0.2 * (q09 * q15)
        H += 0.3 * (q10 * q15)
        H += 0.0 * (q11 * q15)


        #コンパイル
        qubo, offset = Compile(H).get_qubo()
        expected_qubo_matrix = \
            [[-2.0, 2.0, 2.0, 2.0, 2.0, 0.3, 0.2, 0.6, 2.0, 0.0, 0.0, 0.0, 2.0, 0.0,\
              0.0, 0.0],\
             [0.0, -2.0, 2.0, 2.0, 0.3, 2.0, 0.1, 0.2, 0.0, 2.0, 0.0, 0.0, 0.0, 2.0,\
              0.0, 0.0],\
             [0.0, 0.0, -2.0, 2.0, 0.2, 0.1, 2.0, 0.3, 0.0, 0.0, 2.0, 0.0, 0.0, 0.0,\
              2.0, 0.0],\
             [0.0, 0.0, 0.0, -2.0, 0.6, 0.2, 0.3, 2.0, 0.0, 0.0, 0.0, 2.0, 0.0, 0.0,\
              0.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, -2.0, 2.0, 2.0, 2.0, 2.0, 0.3, 0.2, 0.6, 2.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, -2.0, 2.0, 2.0, 0.3, 2.0, 0.1, 0.2, 0.0, 2.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -2.0, 2.0, 0.2, 0.1, 2.0, 0.3, 0.0, 0.0,\
              2.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -2.0, 0.6, 0.2, 0.3, 2.0, 0.0, 0.0,\
              0.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -2.0, 2.0, 2.0, 2.0, 2.0, 0.3,\
              0.2, 0.6],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -2.0, 2.0, 2.0, 0.3, 2.0,\
              0.1, 0.2],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -2.0, 2.0, 0.2, 0.1,\
              2.0, 0.3],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -2.0, 0.6, 0.2,\
              0.3, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -2.0, 2.0,\
              2.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -2.0,\
              2.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              -2.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, -2.0]]
        self.assertTrue(are_same_qubo_matrices(qubo[0], expected_qubo_matrix))
        self.assertEqual(offset, 8)

    def test_clustering(self):
        #データ
        x = np.array([0.45, 0.80, 0.71, 0.49, 0.79, 0.30, 0.44, 0.14, 0.30, 0.40])
        y = np.array([0.14, 0.14, 0.17, 0.25, 0.32, 0.63, 0.68, 0.74, 0.77, 0.84])

        #量子ビットを用意する、例：q0_1は点0のクラス1担当
        q0_1, q0_2, q0_3 = symbols('q0_1 q0_2 q0_3')
        q1_1, q1_2, q1_3 = symbols('q1_1 q1_2 q1_3')
        q2_1, q2_2, q2_3 = symbols('q2_1 q2_2 q2_3')
        q3_1, q3_2, q3_3 = symbols('q3_1 q3_2 q3_3')
        q4_1, q4_2, q4_3 = symbols('q4_1 q4_2 q4_3')
        q5_1, q5_2, q5_3 = symbols('q5_1 q5_2 q5_3')
        q6_1, q6_2, q6_3 = symbols('q6_1 q6_2 q6_3')
        q7_1, q7_2, q7_3 = symbols('q7_1 q7_2 q7_3')
        q8_1, q8_2, q8_3 = symbols('q8_1 q8_2 q8_3')
        q9_1, q9_2, q9_3 = symbols('q9_1 q9_2 q9_3')

        #制約条件：各点ともワンホットで一つだけ１になる
        H = 0
        H += (q0_1 + q0_2 + q0_3 - 1)**2
        H += (q1_1 + q1_2 + q1_3 - 1)**2
        H += (q2_1 + q2_2 + q2_3 - 1)**2
        H += (q3_1 + q3_2 + q3_3 - 1)**2
        H += (q4_1 + q4_2 + q4_3 - 1)**2
        H += (q5_1 + q5_2 + q5_3 - 1)**2
        H += (q6_1 + q6_2 + q6_3 - 1)**2
        H += (q7_1 + q7_2 + q7_3 - 1)**2
        H += (q8_1 + q8_2 + q8_3 - 1)**2
        H += (q9_1 + q9_2 + q9_3 - 1)**2

        #コスト：2点の組み合わせで
        for i in range(9):
            for j in range(i+1, 10):
                #2点の距離
                dist = ((x[i] - x[j])**2 + (y[i] - y[j])**2)**0.5

                #同時にクラス1に入った場合のペナルティ
                text = f'H += 0.1 * {dist} * (q{i}_1 * q{j}_1)'
                exec(text)

                #同時にクラス2に入った場合のペナルティ
                text = f'H += 0.1 * {dist} * (q{i}_2 * q{j}_2)'
                exec(text)

                #同時にクラス3に入った場合のペナルティ
                text = f'H += 0.1 * {dist} * (q{i}_3 * q{j}_3)'
                exec(text)

        #コンパイル
        qubo, offset = Compile(H).get_qubo()
        expected_qubo_matrix = \
            [[-1.0, 2.0, 2.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, -1.0, 2.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, -1.0, 2.0, 2.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, -1.0, 2.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 2.0, 2.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 2.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 2.0, 2.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 2.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 2.0,\
              2.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0,\
              2.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, -1.0, 2.0, 2.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, -1.0, 2.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, -1.0, 2.0, 2.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 2.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 2.0, 2.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 2.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 2.0, 2.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 2.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 0.0,\
              0.0, 0.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0,\
              2.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              -1.0, 2.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, -1.0]]
        self.assertTrue(are_same_qubo_matrices(qubo[0], expected_qubo_matrix))
        self.assertEqual(offset, 10)

    def test_simultaneous_equations1(self):
        #量子ビットを用意する
        x = symbols('x')
        y = symbols('y')
        z = symbols('z')

        #連立方程式の設定
        H = 0
        H += ( 5*x -  y +2*z - 7)**2
        H += (-3*x +4*y +  z + 2)**2
        H += (   x -2*y -4*z + 3)**2

        #コンパイル
        qubo, offset = Compile(H).get_qubo()
        expected_qubo_matrix = \
            [[-41.0, -38.0, 6.0],\
             [0.0, 39.0, 20.0],\
             [0.0, 0.0, -27.0]]
        self.assertTrue(are_same_qubo_matrices(qubo[0], expected_qubo_matrix))
        self.assertEqual(offset, 62)

    def test_simultaneous_equations2(self):
        #量子ビットを用意する
        x0, x1 = symbols('x0 x1')
        y0, y1 = symbols('y0 y1')
        z0, z1 = symbols('z0 z1')

        #x,y,zを2進数（2bit）で表す
        x = 2*x0 + 1*x1
        y = 2*y0 + 1*y1
        z = 2*z0 + 1*z1

        #連立方程式の設定
        H = 0
        H += (  x +  y +  z -  6)**2
        H += (2*x +3*y -2*z - 11)**2
        H += (3*x -  y +  z -  4)**2

        #コンパイル
        qubo, offset = Compile(H).get_qubo()
        expected_qubo_matrix = \
            [[-104.0, 56.0, 32.0, 16.0, 0.0, 0.0],\
             [0.0, -66.0, 16.0, 8.0, 0.0, 0.0],\
             [0.0, 0.0, -96.0, 44.0, -48.0, -24.0],\
             [0.0, 0.0, 0.0, -59.0, -24.0, -12.0],\
             [0.0, 0.0, 0.0, 0.0, 72.0, 24.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 30.0]]
        self.assertTrue(are_same_qubo_matrices(qubo[0], expected_qubo_matrix))
        self.assertEqual(offset, 173)

    def test_simultaneous_equations3(self):
        #量子ビットを用意する
        x0, x1, x2, x3, x4, x5, x6, x7 = symbols('x0 x1 x2 x3 x4 x5 x6 x7')
        y0, y1, y2, y3, y4, y5, y6, y7 = symbols('y0 y1 y2 y3 y4 y5 y6 y7')
        z0, z1, z2, z3, z4, z5, z6, z7 = symbols('z0 z1 z2 z3 z4 z5 z6 z7')

        #x,y,zを2進数（8bit）で表す
        x = 128*x0 + 64*x1 + 32*x2 + 16*x3 + 8*x4 + 4*x5 + 2*x6 + 1*x7
        y = 128*y0 + 64*y1 + 32*y2 + 16*y3 + 8*y4 + 4*y5 + 2*y6 + 1*y7
        z = 128*z0 + 64*z1 + 32*z2 + 16*z3 + 8*z4 + 4*z5 + 2*z6 + 1*z7

        #連立方程式の設定
        H = 0
        H += (10*x +14*y +4*z - 5120)**2
        H += ( 9*x +12*y +2*z - 4230)**2
        H += ( 7*x + 5*y +2*z - 2360)**2


        #コンパイル
        qubo, offset = Compile(H).get_qubo()
        expected_qubo_matrix = \
            [[-23313920.0, 3768320.0, 1884160.0, 942080.0, 471040.0, 235520.0,\
              117760.0, 58880.0, 9273344.0, 4636672.0, 2318336.0, 1159168.0, 579584.0,\
              289792.0, 144896.0, 72448.0, 2359296.0, 1179648.0, 589824.0, 294912.0,\
              147456.0, 73728.0, 36864.0, 18432.0],\
             [0.0, -12599040.0, 942080.0, 471040.0, 235520.0, 117760.0, 58880.0,\
              29440.0, 4636672.0, 2318336.0, 1159168.0, 579584.0, 289792.0, 144896.0,\
              72448.0, 36224.0, 1179648.0, 589824.0, 294912.0, 147456.0, 73728.0,\
              36864.0, 18432.0, 9216.0],\
             [0.0, 0.0, -6535040.0, 235520.0, 117760.0, 58880.0, 29440.0, 14720.0,\
              2318336.0, 1159168.0, 579584.0, 289792.0, 144896.0, 72448.0, 36224.0,\
              18112.0, 589824.0, 294912.0, 147456.0, 73728.0, 36864.0, 18432.0,\
              9216.0, 4608.0],\
             [0.0, 0.0, 0.0, -3326400.0, 58880.0, 29440.0, 14720.0, 7360.0, 1159168.0,\
              579584.0, 289792.0, 144896.0, 72448.0, 36224.0, 18112.0, 9056.0,\
              294912.0, 147456.0, 73728.0, 36864.0, 18432.0, 9216.0, 4608.0, 2304.0],\
             [0.0, 0.0, 0.0, 0.0, -1677920.0, 14720.0, 7360.0, 3680.0, 579584.0,\
              289792.0, 144896.0, 72448.0, 36224.0, 18112.0, 9056.0, 4528.0, 147456.0,\
              73728.0, 36864.0, 18432.0, 9216.0, 4608.0, 2304.0, 1152.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, -842640.0, 3680.0, 1840.0, 289792.0, 144896.0,\
              72448.0, 36224.0, 18112.0, 9056.0, 4528.0, 2264.0, 73728.0, 36864.0,\
              18432.0, 9216.0, 4608.0, 2304.0, 1152.0, 576.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -422240.0, 920.0, 144896.0, 72448.0,\
              36224.0, 18112.0, 9056.0, 4528.0, 2264.0, 1132.0, 36864.0, 18432.0,\
              9216.0, 4608.0, 2304.0, 1152.0, 576.0, 288.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -211350.0, 72448.0, 36224.0, 18112.0,\
              9056.0, 4528.0, 2264.0, 1132.0, 566.0, 18432.0, 9216.0, 4608.0, 2304.0,\
              1152.0, 576.0, 288.0, 144.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -28385280.0, 5980160.0,\
              2990080.0, 1495040.0, 747520.0, 373760.0, 186880.0, 93440.0, 2949120.0,\
              1474560.0, 737280.0, 368640.0, 184320.0, 92160.0, 46080.0, 23040.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -15687680.0, 1495040.0,\
              747520.0, 373760.0, 186880.0, 93440.0, 46720.0, 1474560.0, 737280.0,\
              368640.0, 184320.0, 92160.0, 46080.0, 23040.0, 11520.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -8217600.0, 373760.0,\
              186880.0, 93440.0, 46720.0, 23360.0, 737280.0, 368640.0, 184320.0,\
              92160.0, 46080.0, 23040.0, 11520.0, 5760.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -4202240.0,\
              93440.0, 46720.0, 23360.0, 11680.0, 368640.0, 184320.0, 92160.0,\
              46080.0, 23040.0, 11520.0, 5760.0, 2880.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -2124480.0,\
              23360.0, 11680.0, 5840.0, 184320.0, 92160.0, 46080.0, 23040.0, 11520.0,\
              5760.0, 2880.0, 1440.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              -1068080.0, 5840.0, 2920.0, 92160.0, 46080.0, 23040.0, 11520.0, 5760.0,\
              2880.0, 1440.0, 720.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              -535500.0, 1460.0, 46080.0, 23040.0, 11520.0, 5760.0, 2880.0, 1440.0,\
              720.0, 360.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, -268115.0, 23040.0, 11520.0, 5760.0, 2880.0, 1440.0, 720.0, 360.0,\
              180.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, -8223744.0, 393216.0, 196608.0, 98304.0, 49152.0, 24576.0,\
              12288.0, 6144.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, -4210176.0, 98304.0, 49152.0, 24576.0, 12288.0, 6144.0,\
              3072.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, -2129664.0, 24576.0, 12288.0, 6144.0, 3072.0,\
              1536.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, -1070976.0, 6144.0, 3072.0, 1536.0, 768.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -537024.0, 1536.0, 768.0, 384.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -268896.0, 384.0, 192.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -134544.0, 96.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -67296.0]]
        self.assertTrue(are_same_qubo_matrices(qubo[0], expected_qubo_matrix))
        self.assertEqual(offset, 49676900)

    def test_simultaneous_equations4(self):
        #量子ビットをNビット表現で用意する
        x = symbols_nbit(0, 256, 'x{}', num=8)
        y = symbols_nbit(0, 256, 'y{}', num=8)
        z = symbols_nbit(0, 256, 'z{}', num=8)

        #連立方程式の設定
        H = 0
        H += (10*x +14*y +4*z - 5120)**2
        H += ( 9*x +12*y +2*z - 4230)**2
        H += ( 7*x + 5*y +2*z - 2360)**2

        #コンパイル
        qubo, offset = Compile(H).get_qubo()
        expected_qubo_matrix = \
            [[-23313920.0, 3768320.0, 1884160.0, 942080.0, 471040.0, 235520.0,\
              117760.0, 58880.0, 9273344.0, 4636672.0, 2318336.0, 1159168.0, 579584.0,\
              289792.0, 144896.0, 72448.0, 2359296.0, 1179648.0, 589824.0, 294912.0,\
              147456.0, 73728.0, 36864.0, 18432.0],\
             [0.0, -12599040.0, 942080.0, 471040.0, 235520.0, 117760.0, 58880.0,\
              29440.0, 4636672.0, 2318336.0, 1159168.0, 579584.0, 289792.0, 144896.0,\
              72448.0, 36224.0, 1179648.0, 589824.0, 294912.0, 147456.0, 73728.0,\
              36864.0, 18432.0, 9216.0],\
             [0.0, 0.0, -6535040.0, 235520.0, 117760.0, 58880.0, 29440.0, 14720.0,\
              2318336.0, 1159168.0, 579584.0, 289792.0, 144896.0, 72448.0, 36224.0,\
              18112.0, 589824.0, 294912.0, 147456.0, 73728.0, 36864.0, 18432.0,\
              9216.0, 4608.0],\
             [0.0, 0.0, 0.0, -3326400.0, 58880.0, 29440.0, 14720.0, 7360.0, 1159168.0,\
              579584.0, 289792.0, 144896.0, 72448.0, 36224.0, 18112.0, 9056.0,\
              294912.0, 147456.0, 73728.0, 36864.0, 18432.0, 9216.0, 4608.0, 2304.0],\
             [0.0, 0.0, 0.0, 0.0, -1677920.0, 14720.0, 7360.0, 3680.0, 579584.0,\
              289792.0, 144896.0, 72448.0, 36224.0, 18112.0, 9056.0, 4528.0, 147456.0,\
              73728.0, 36864.0, 18432.0, 9216.0, 4608.0, 2304.0, 1152.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, -842640.0, 3680.0, 1840.0, 289792.0, 144896.0,\
              72448.0, 36224.0, 18112.0, 9056.0, 4528.0, 2264.0, 73728.0, 36864.0,\
              18432.0, 9216.0, 4608.0, 2304.0, 1152.0, 576.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -422240.0, 920.0, 144896.0, 72448.0,\
              36224.0, 18112.0, 9056.0, 4528.0, 2264.0, 1132.0, 36864.0, 18432.0,\
              9216.0, 4608.0, 2304.0, 1152.0, 576.0, 288.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -211350.0, 72448.0, 36224.0, 18112.0,\
              9056.0, 4528.0, 2264.0, 1132.0, 566.0, 18432.0, 9216.0, 4608.0, 2304.0,\
              1152.0, 576.0, 288.0, 144.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -28385280.0, 5980160.0,\
              2990080.0, 1495040.0, 747520.0, 373760.0, 186880.0, 93440.0, 2949120.0,\
              1474560.0, 737280.0, 368640.0, 184320.0, 92160.0, 46080.0, 23040.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -15687680.0, 1495040.0,\
              747520.0, 373760.0, 186880.0, 93440.0, 46720.0, 1474560.0, 737280.0,\
              368640.0, 184320.0, 92160.0, 46080.0, 23040.0, 11520.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -8217600.0, 373760.0,\
              186880.0, 93440.0, 46720.0, 23360.0, 737280.0, 368640.0, 184320.0,\
              92160.0, 46080.0, 23040.0, 11520.0, 5760.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -4202240.0,\
              93440.0, 46720.0, 23360.0, 11680.0, 368640.0, 184320.0, 92160.0,\
              46080.0, 23040.0, 11520.0, 5760.0, 2880.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -2124480.0,\
              23360.0, 11680.0, 5840.0, 184320.0, 92160.0, 46080.0, 23040.0, 11520.0,\
              5760.0, 2880.0, 1440.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              -1068080.0, 5840.0, 2920.0, 92160.0, 46080.0, 23040.0, 11520.0, 5760.0,\
              2880.0, 1440.0, 720.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              -535500.0, 1460.0, 46080.0, 23040.0, 11520.0, 5760.0, 2880.0, 1440.0,\
              720.0, 360.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, -268115.0, 23040.0, 11520.0, 5760.0, 2880.0, 1440.0, 720.0, 360.0,\
              180.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, -8223744.0, 393216.0, 196608.0, 98304.0, 49152.0, 24576.0,\
              12288.0, 6144.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, -4210176.0, 98304.0, 49152.0, 24576.0, 12288.0, 6144.0,\
              3072.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, -2129664.0, 24576.0, 12288.0, 6144.0, 3072.0,\
              1536.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, -1070976.0, 6144.0, 3072.0, 1536.0, 768.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -537024.0, 1536.0, 768.0, 384.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -268896.0, 384.0, 192.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -134544.0, 96.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -67296.0]]
        self.assertTrue(are_same_qubo_matrices(qubo[0], expected_qubo_matrix))
        self.assertEqual(offset, 49676900)

    def test_linear_regression1(self):
        #量子ビットを用意する
        a0, a1, a2, a3, a4, a5, a6, a7 = symbols('a0 a1 a2 a3 a4 a5 a6 a7')
        b0, b1, b2, b3, b4, b5, b6, b7 = symbols('b0 b1 b2 b3 b4 b5 b6 b7')

        #aを2進数（8bit）で表す、10～20で規格化
        a = 10 + 10 * ((128*a0 + 64*a1 + 32*a2 + 16*a3 + 8*a4 + 4*a5 + 2*a6 + 1*a7) / 256)
        # print(a)
        #bを2進数（8bit）で表す、0～1で規格化
        b = 0 + 1 * ((128*b0 + 64*b1 + 32*b2 + 16*b3 + 8*b4 + 4*b5 + 2*b6 + 1*b7) / 256)

        #各点の誤差二乗をペナルティとする
        H = 0
        H += (5.75 - (a*0.31 + b))**2
        H += (8.56 - (a*0.4 + b))**2
        H += (8.42 - (a*0.47 + b))**2
        H += (7.78 - (a*0.4 + b))**2
        H += (10.25 - (a*0.54 + b))**2
        H += (6.79 - (a*0.36 + b))**2
        H += (11.51 - (a*0.56 + b))**2
        H += (7.66 - (a*0.43 + b))**2
        H += (6.99 - (a*0.32 + b))**2
        H += (10.61 - (a*0.6 + b))**2


        #コンパイル
        qubo, offset = Compile(H).get_qubo()
        expected_qubo_matrix = \
            [[-133.58749999999998, 50.477500000000006, 25.238750000000003,\
              12.619375000000002, 6.309687500000001, 3.1548437500000004,\
              1.5774218750000002, 0.7887109375000001, 21.95, 10.975, 5.4875, 2.74375,\
              1.371875, 0.6859375, 0.34296875, 0.171484375],\
             [0.0, -79.413125, 12.619375000000002, 6.309687500000001,\
              3.1548437500000004, 1.5774218750000002, 0.7887109375000001,\
              0.39435546875000005, 10.975, 5.4875, 2.74375, 1.371875, 0.6859375,\
              0.34296875, 0.171484375, 0.0857421875],\
             [0.0, 0.0, -42.86140625, 3.1548437500000004, 1.5774218750000002,\
              0.7887109375000001, 0.39435546875000005, 0.19717773437500002, 5.4875,\
              2.74375, 1.371875, 0.6859375, 0.34296875, 0.171484375, 0.0857421875,\
              0.04287109375],\
             [0.0, 0.0, 0.0, -22.2194140625, 0.7887109375000001, 0.39435546875000005,\
              0.19717773437500002, 0.09858886718750001, 2.74375, 1.371875, 0.6859375,\
              0.34296875, 0.171484375, 0.0857421875, 0.04287109375, 0.021435546875],\
             [0.0, 0.0, 0.0, 0.0, -11.306884765625, 0.19717773437500002,\
              0.09858886718750001, 0.049294433593750006, 1.371875, 0.6859375,\
              0.34296875, 0.171484375, 0.0857421875, 0.04287109375, 0.021435546875,\
              0.0107177734375],\
             [0.0, 0.0, 0.0, 0.0, 0.0, -5.70273681640625, 0.049294433593750006,\
              0.024647216796875003, 0.6859375, 0.34296875, 0.171484375, 0.0857421875,\
              0.04287109375, 0.021435546875, 0.0107177734375, 0.00535888671875],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -2.8636920166015623, 0.012323608398437502,\
              0.34296875, 0.171484375, 0.0857421875, 0.04287109375, 0.021435546875,\
              0.0107177734375, 0.00535888671875, 0.002679443359375],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.4349269104003906, 0.171484375,\
              0.0857421875, 0.04287109375, 0.021435546875, 0.0107177734375,\
              0.00535888671875, 0.002679443359375, 0.0013397216796875],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -37.92, 2.5, 1.25, 0.625,\
              0.3125, 0.15625, 0.078125, 0.0390625],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -19.585, 0.625, 0.3125,\
              0.15625, 0.078125, 0.0390625, 0.01953125],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -9.94875, 0.15625,\
              0.078125, 0.0390625, 0.01953125, 0.009765625],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -5.0134375,\
              0.0390625, 0.01953125, 0.009765625, 0.0048828125],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              -2.516484375, 0.009765625, 0.0048828125, 0.00244140625],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              -1.26068359375, 0.00244140625, 0.001220703125],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              -0.6309521484375, 0.0006103515625],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, -0.315628662109375]]
        self.assertTrue(are_same_qubo_matrices(qubo[0], expected_qubo_matrix))
        self.assertEqual(offset, 171.475400000000)

    def test_linear_regression2(self):
        #aを2進数（8bit）で表す、10～20で規格化
        a = symbols_nbit(10, 20, 'a{}', num=8)
        #bを2進数（8bit）で表す、0～1で規格化
        b = symbols_nbit(0, 1, 'b{}', num=8)

        #誤差二乗ペナルティ
        H = 0
        H += (5.75 - (a*0.31 + b))**2
        H += (8.56 - (a*0.4 + b))**2
        H += (8.42 - (a*0.47 + b))**2
        H += (7.78 - (a*0.4 + b))**2
        H += (10.25 - (a*0.54 + b))**2
        H += (6.79 - (a*0.36 + b))**2
        H += (11.51 - (a*0.56 + b))**2
        H += (7.66 - (a*0.43 + b))**2
        H += (6.99 - (a*0.32 + b))**2
        H += (10.61 - (a*0.6 + b))**2


        #コンパイル
        qubo, offset = Compile(H).get_qubo()
        expected_qubo_matrix = \
            [[-570.04, 201.91000000000003, 100.95500000000001, 50.477500000000006,\
              25.238750000000003, 12.619375000000002, 6.309687500000001,\
              3.1548437500000004, 43.900000000000006, 21.950000000000003,\
              10.975000000000001, 5.487500000000001, 2.7437500000000004,\
              1.3718750000000002, 0.6859375000000001, 0.34296875000000004],\
             [0.0, -335.4975, 50.477500000000006, 25.238750000000003,\
              12.619375000000002, 6.309687500000001, 3.1548437500000004,\
              1.5774218750000002, 21.950000000000003, 10.975000000000001,\
              5.487500000000001, 2.7437500000000004, 1.3718750000000002,\
              0.6859375000000001, 0.34296875000000004, 0.17148437500000002],\
             [0.0, 0.0, -180.36812500000002, 12.619375000000002, 6.309687500000001,\
              3.1548437500000004, 1.5774218750000002, 0.7887109375000001,\
              10.975000000000001, 5.487500000000001, 2.7437500000000004,\
              1.3718750000000002, 0.6859375000000001, 0.34296875000000004,\
              0.17148437500000002, 0.08574218750000001],\
             [0.0, 0.0, 0.0, -93.33890625000001, 3.1548437500000004,\
              1.5774218750000002, 0.7887109375000001, 0.39435546875000005,\
              5.487500000000001, 2.7437500000000004, 1.3718750000000002,\
              0.6859375000000001, 0.34296875000000004, 0.17148437500000002,\
              0.08574218750000001, 0.042871093750000006],\
             [0.0, 0.0, 0.0, 0.0, -47.4581640625, 0.7887109375000001,\
              0.39435546875000005, 0.19717773437500002, 2.7437500000000004,\
              1.3718750000000002, 0.6859375000000001, 0.34296875000000004,\
              0.17148437500000002, 0.08574218750000001, 0.042871093750000006,\
              0.021435546875000003],\
             [0.0, 0.0, 0.0, 0.0, 0.0, -23.926259765625, 0.19717773437500002,\
              0.09858886718750001, 1.3718750000000002, 0.6859375000000001,\
              0.34296875000000004, 0.17148437500000002, 0.08574218750000001,\
              0.042871093750000006, 0.021435546875000003, 0.010717773437500001],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -12.01242431640625, 0.049294433593750006,\
              0.6859375000000001, 0.34296875000000004, 0.17148437500000002,\
              0.08574218750000001, 0.042871093750000006, 0.021435546875000003,\
              0.010717773437500001, 0.005358886718750001],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -6.018535766601563,\
              0.34296875000000004, 0.17148437500000002, 0.08574218750000001,\
              0.042871093750000006, 0.021435546875000003, 0.010717773437500001,\
              0.005358886718750001, 0.0026794433593750003],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -81.82000000000001, 2.5, 1.25,\
              0.625, 0.3125, 0.15625, 0.078125, 0.0390625],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -41.535000000000004, 0.625,\
              0.3125, 0.15625, 0.078125, 0.0390625, 0.01953125],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -20.923750000000002,\
              0.15625, 0.078125, 0.0390625, 0.01953125, 0.009765625],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              -10.500937500000001, 0.0390625, 0.01953125, 0.009765625, 0.0048828125],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              -5.2602343750000005, 0.009765625, 0.0048828125, 0.00244140625],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              -2.6325585937500002, 0.00244140625, 0.001220703125],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              -1.3168896484375001, 0.0006103515625],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,\
              0.0, -0.6585974121093751]]
        self.assertTrue(are_same_qubo_matrices(qubo[0], expected_qubo_matrix))
        self.assertEqual(offset, 741.515400000000)

    def test_napzak1(self):
        #量子ビットを用意する
        q0 = symbols('q0')
        q1 = symbols('q1')
        q2 = symbols('q2')
        q3 = symbols('q3')
        q4 = symbols('q4')
        q5 = symbols('q5')
        q6 = symbols('q6')

        #7個のドリンクをそれぞれ取るか取らないか、値段を係数にして合計が15になる（強い条件）
        H = 0
        H += (4*q0 + 4*q1 + 4*q2 + 6*q3 + 6*q4 + 7*q5 + 7*q6 - 15)**2

        #7個のドリンクをそれぞれ取るか取らないか、回復量を係数にして報酬とする（弱い条件）
        H += -0.01 * (3*q0 + 3*q1 + 3*q2 + 5*q3 + 5*q4 + 7*q5 + 7*q6)

        #コンパイル
        qubo, offset = Compile(H).get_qubo()
        expected_qubo_matrix = \
            [[-104.03, 32.0, 32.0, 48.0, 48.0, 56.0, 56.0],\
             [0.0, -104.03, 32.0, 48.0, 48.0, 56.0, 56.0],\
             [0.0, 0.0, -104.03, 48.0, 48.0, 56.0, 56.0],\
             [0.0, 0.0, 0.0, -144.05, 72.0, 84.0, 84.0],\
             [0.0, 0.0, 0.0, 0.0, -144.05, 84.0, 84.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, -161.07, 98.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -161.07]]
        self.assertTrue(are_same_qubo_matrices(qubo[0], expected_qubo_matrix))
        self.assertEqual(offset, 225)

    def test_napzak2(self):
        #量子ビットを用意する
        q0 = symbols('q0')
        q1 = symbols('q1')
        q2 = symbols('q2')
        q3 = symbols('q3')
        q4 = symbols('q4')
        q5 = symbols('q5')
        q6 = symbols('q6')

        #補助ビットを用意する
        s0 = symbols('s0')
        s1 = symbols('s1')
        s2 = symbols('s2')
        s3 = symbols('s3')

        #補助ビットをワンホットにする（強い条件）
        #これにより (12*s0 + 13*s1 + 14*s2 + 15*s3) で 「12 or 13 or 14 or 15」 を表現できる
        H = 0
        H += (s0 + s1 + s2 + s3 - 1)**2

        #7個のドリンクをそれぞれ取るか取らないか、値段を係数にして合計が「12 or 13 or 14 or 15」になる（強い条件）
        H += (4*q0 + 4*q1 + 4*q2 + 6*q3 + 6*q4 + 7*q5 + 7*q6 - (12*s0 + 13*s1 + 14*s2 + 15*s3))**2

        #7個のドリンクをそれぞれ取るか取らないか、回復量を係数にして報酬とする（弱い条件）
        H += -0.01 * (3*q0 + 3*q1 + 3*q2 + 5*q3 + 5*q4 + 7*q5 + 7*q6)

        #おいしいみずを降順にする
        H += (1 - q0) * q1
        H += (1 - q1) * q2
        #サイコソーダを降順にする
        H += (1 - q3) * q4
        #ミックスオレを降順にする
        H += (1 - q5) * q6

        #コンパイル
        qubo, offset = Compile(H).get_qubo()
        # print(np.array2string(qubo[0], separator=', ', formatter={'float_kind': lambda x: f'{x}'}))
        expected_qubo_matrix = \
            [[15.97, 31.0, 32.0, 48.0, 48.0, 56.0, 56.0, -96.0, -104.0, -112.0,\
              -120.0],\
             [0.0, 16.97, 31.0, 48.0, 48.0, 56.0, 56.0, -96.0, -104.0, -112.0, -120.0],\
             [0.0, 0.0, 16.97, 48.0, 48.0, 56.0, 56.0, -96.0, -104.0, -112.0, -120.0],\
             [0.0, 0.0, 0.0, 35.95, 71.0, 84.0, 84.0, -144.0, -156.0, -168.0, -180.0],\
             [0.0, 0.0, 0.0, 0.0, 36.95, 84.0, 84.0, -144.0, -156.0, -168.0, -180.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 48.93, 97.0, -168.0, -182.0, -196.0, -210.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 49.93, -168.0, -182.0, -196.0, -210.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 143.0, 314.0, 338.0, 362.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 168.0, 366.0, 392.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 195.0, 422.0],\
             [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 224.0]]
        self.assertTrue(are_same_qubo_matrices(qubo[0], expected_qubo_matrix))
        self.assertEqual(offset, 1)


# テストケースの定義
@pytest.mark.parametrize("expr, expected", [
    (symbols('x')**2 + 3*symbols('x') + 2, 2),  # 単純な多項式
    (symengine.expand(5), 0),  # 定数項のみ
    (symbols('x'), 1),  # 単一変数
    (symbols('x')**2 + symbols('y')**2, 2),  # 複数変数の多項式
    (symbols('x')**(symbols('x')**2), None),  # 非多項式（指数に変数を含む）
    (symbols('x')**0.5, None),  # 非多項式（根号内に変数）
    (3*symbols('x')**3 * 2*symbols('x')**2, 5),  # 乗算された項
    (symbols('x')**3 + 2*symbols('x')**2 * symbols('x'), 3),  # 加算と乗算の混合
    (2*symbols('x') * 3*symbols('y'), 2),  # 複数項の乗算（変数と定数）
    (symbols('x')**-2, None),  # 負のべき乗
])

def test_calc_degree(expr, expected):
    assert calc_degree(expr) == expected
