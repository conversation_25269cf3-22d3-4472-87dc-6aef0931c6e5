#!/usr/bin/env python3
"""
Simple example showing how to use the QAIASampler.

This example demonstrates the basic usage of QAIASampler for solving
QUBO problems with automatic QUBO-to-Ising conversion.
"""

from tytan import symbols, Compile
from tytan.qaia_sampler import QAIASampler


def main():
    print("QAIASampler Basic Usage Example")
    print("=" * 40)
    
    # Step 1: Create a simple optimization problem
    print("\n1. Creating a simple constraint problem:")
    print("   Find x, y, z such that exactly 2 variables are 1")
    
    x, y, z = symbols('x y z')
    expr = (x + y + z - 2)**2  # Penalty for not having exactly 2 variables = 1
    
    print(f"   Expression: {expr}")
    
    # Step 2: Compile to QUBO format
    print("\n2. Compiling to QUBO format:")
    qubo, offset = Compile(expr).get_qubo()
    matrix, index_map = qubo
    
    print("qubo", qubo)
    print(f"   QUBO matrix shape: {matrix.shape}")
    print(f"   Variables: {list(index_map.keys())}")
    print(f"   Offset: {offset}")
    
    # Step 3: Solve using QAIASampler
    print("\n3. Solving with QAIASampler:")
    
    # Initialize sampler with ASB algorithm
    sampler = QAIASampler(
        algorithm='asb',        # Choose from 'asb', 'bsb', 'dsb'
        seed=42,               # For reproducible results
        backend='cpu-float32'  # Computation backend
    )
    
    # Run the sampler
    result = sampler.run(
        qubo,           # QUBO problem (matrix, index_map)
        shots=20,       # Number of samples
        n_iter=100,     # Number of iterations
        dt=0.1,         # Time step (smaller = more stable)
        xi=0.1          # Coupling strength (smaller = more stable)
    )
    print("result",result)
    print(f"   Algorithm: ASB")
    print(f"   Samples: 20")
    print(f"   Iterations: 100")
    
    # Step 4: Analyze results
    print("\n4. Results:")
    print(f"   Found {len(result)} unique solutions")
    
    # Show top 3 solutions
    for i, (solution, energy, count) in enumerate(result[:3]):
        print(f"\n   Solution {i+1}:")
        print(f"     Variables: {solution}")
        print(f"     Energy: {energy}")
        print(f"     Count: {count}")
        
        # Check constraint satisfaction
        total = sum(solution.values())
        constraint_satisfied = (total == 2)
        print(f"     Constraint (sum=2): {total} ✓" if constraint_satisfied else f"     Constraint (sum=2): {total} ✗")
    
    # Step 5: Compare different algorithms
    print("\n5. Comparing different QAIA algorithms:")
    
    algorithms = ['asb', 'bsb', 'dsb']
    best_energies = {}
    
    for algorithm in algorithms:
        try:
            sampler = QAIASampler(algorithm=algorithm, seed=42, backend='cpu-float32')
            result = sampler.run(qubo, shots=10, n_iter=50, dt=0.1, xi=0.1)
            best_energies[algorithm] = result[0][1]
            print(f"   {algorithm.upper()}: best energy = {result[0][1]:.6f}")
        except Exception as e:
            print(f"   {algorithm.upper()}: failed ({e})")
    
    if best_energies:
        best_algo = min(best_energies.keys(), key=lambda k: best_energies[k])
        print(f"\n   Best algorithm for this problem: {best_algo.upper()}")
    
    print("\n" + "=" * 40)
    print("✅ QAIASampler example completed!")
    print("\nKey features demonstrated:")
    print("- Automatic QUBO to Ising conversion")
    print("- Multiple QAIA algorithms (ASB, BSB, DSB)")
    print("- Consistent output format")
    print("- Parameter tuning for stability")


if __name__ == "__main__":
    main()
