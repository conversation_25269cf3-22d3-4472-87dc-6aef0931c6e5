#!/usr/bin/env python3
"""
Simple example showing how to use the enhanced symbol creation functionality.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tytan'))

from tytan import symbols, symbols_list, Compile

def main():
    print("Enhanced TYTAN Library - Ising Symbol Support")
    print("=" * 50)
    
    # Example 1: Basic Ising symbols
    print("\n1. Creating Ising symbols:")
    s1, s2 = symbols('s1 s2', symbol_type="ising")
    print(f"   Created Ising symbols: s1, s2")
    
    # Example 2: Ising constraint problem
    print("\n2. Ising constraint: s1 should equal s2")
    H_ising = (s1 - s2)**2
    print(f"   Ising formulation: (s1 - s2)^2")
    
    # Convert to QUBO automatically
    qubo, offset = Compile(H_ising).get_qubo()
    print(f"   ✓ Automatically converted to QUBO")
    print(f"   QUBO matrix shape: {qubo[0].shape}")
    print(f"   Offset: {offset}")
    
    # Example 3: Mixed binary and Ising
    print("\n3. Mixed variable types:")
    x = symbols('x', symbol_type="binary")  # Binary variable
    s = symbols('s', symbol_type="ising")   # Ising variable
    
    H_mixed = x + s  # Simple mixed expression
    qubo_mixed, offset_mixed = Compile(H_mixed).get_qubo()
    print(f"   Mixed expression: x (binary) + s (ising)")
    print(f"   ✓ Successfully compiled to QUBO")
    
    # Example 4: Array of Ising symbols
    print("\n4. Array of Ising symbols:")
    ising_array = symbols_list([3, 3], 's{}_{}', symbol_type="ising")
    print(f"   Created 3x3 array of Ising symbols")
    
    # Simple constraint on the array
    H_array = (ising_array[0,0] + ising_array[0,1] + ising_array[0,2] - 1)**2
    qubo_array, offset_array = Compile(H_array).get_qubo()
    print(f"   Constraint: exactly one symbol in first row should be +1")
    print(f"   ✓ Compiled to QUBO successfully")
    
    print("\n" + "=" * 50)
    print("✅ All examples completed successfully!")
    print("\nKey benefits:")
    print("• Natural Ising model formulation with s ∈ {-1, +1}")
    print("• Automatic conversion to QUBO format")
    print("• Full backward compatibility")
    print("• Support for mixed variable types")

if __name__ == "__main__":
    main()
