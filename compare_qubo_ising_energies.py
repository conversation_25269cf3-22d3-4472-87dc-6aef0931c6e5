#!/usr/bin/env python3
"""
Compare QUBO and Ising energies to understand when they are the same vs different.
"""

import sys
sys.path.insert(0, '/Users/<USER>/Documents/snow/tytan-main')

import numpy as np
from tytan import symbols, Compile, clear_symbol_registry
from tytan.qaia_sampler import QAIASampler


def compare_energies_detailed():
    """
    Detailed comparison of QUBO and Ising energies.
    """
    print("🔍 DETAILED COMPARISON: QUBO vs ISING ENERGIES")
    print("=" * 60)
    
    clear_symbol_registry()
    x, y, z = symbols('x y z')
    expr = (x + y + z - 2)**2
    
    # Get QUBO
    qubo, qubo_offset = Compile(expr).get_qubo()
    matrix, index_map = qubo
    
    # Get Ising conversion
    sampler = QAIASampler()
    J, h, ising_index_map, ising_offset = sampler._qubo_to_ising(matrix, index_map)
    
    print(f"Original expression: (x + y + z - 2)²")
    print(f"QUBO offset: {qubo_offset}")
    print(f"Ising offset: {ising_offset}")
    
    print(f"\nQUBO matrix:\n{matrix}")
    print(f"\nIsing J matrix:\n{J}")
    print(f"Ising h vector: {h}")
    
    print(f"\n{'='*80}")
    print(f"{'Binary Sol':<12} {'QUBO Raw':<10} {'QUBO+Off':<10} {'Ising Raw':<11} {'Ising+Off':<11} {'Original':<10} {'Match?':<8}")
    print(f"{'='*80}")
    
    all_solutions = [
        [0, 0, 0], [0, 0, 1], [0, 1, 0], [0, 1, 1],
        [1, 0, 0], [1, 0, 1], [1, 1, 0], [1, 1, 1]
    ]
    
    for binary_sol in all_solutions:
        binary_sol = np.array(binary_sol)
        ising_sol = 2 * binary_sol - 1
        
        # Calculate different energy types
        original_energy = (sum(binary_sol) - 2)**2
        qubo_raw_energy = binary_sol.T @ matrix @ binary_sol
        qubo_total_energy = qubo_raw_energy + qubo_offset
        ising_raw_energy = ising_sol.T @ J @ ising_sol + h.T @ ising_sol
        ising_total_energy = ising_raw_energy + ising_offset
        
        # Check matches
        qubo_ising_raw_match = abs(qubo_raw_energy - ising_raw_energy) < 1e-10
        total_match = abs(qubo_total_energy - original_energy) < 1e-10
        
        print(f"{str(list(binary_sol)):<12} {qubo_raw_energy:<10.1f} {qubo_total_energy:<10.1f} "
              f"{ising_raw_energy:<11.1f} {ising_total_energy:<11.1f} {original_energy:<10.1f} "
              f"{'✓' if qubo_ising_raw_match else '✗':<8}")
    
    return qubo_raw_energy, ising_raw_energy, qubo_total_energy, ising_total_energy


def answer_the_question():
    """
    Direct answer to: Are QUBO and Ising energies the same?
    """
    print(f"\n🎯 ANSWER: Are QUBO and Ising energies the same?")
    print("=" * 60)
    
    clear_symbol_registry()
    x, y, z = symbols('x y z')
    expr = (x + y + z - 2)**2
    
    qubo, qubo_offset = Compile(expr).get_qubo()
    matrix, index_map = qubo
    
    sampler = QAIASampler()
    J, h, ising_index_map, ising_offset = sampler._qubo_to_ising(matrix, index_map)
    
    # Test with one solution
    binary_sol = np.array([1, 1, 0])
    ising_sol = 2 * binary_sol - 1
    
    qubo_raw = binary_sol.T @ matrix @ binary_sol
    ising_raw = ising_sol.T @ J @ ising_sol + h.T @ ising_sol
    
    print(f"📊 For solution [1, 1, 0]:")
    print(f"QUBO raw energy (x^T Q x):           {qubo_raw}")
    print(f"Ising raw energy (s^T J s + h^T s):  {ising_raw}")
    print(f"Are they the same? {abs(qubo_raw - ising_raw) < 1e-10}")
    
    print(f"\n💡 KEY INSIGHTS:")
    print(f"1. ✅ QUBO and Ising RAW energies ARE THE SAME")
    print(f"   - This is guaranteed by the correct conversion formula")
    print(f"   - The mathematical transformation preserves energy equivalence")
    
    print(f"\n2. 🔄 The conversion ensures:")
    print(f"   - x^T Q x = s^T J s + h^T s + ising_offset")
    print(f"   - Where s = 2x - 1 (binary to Ising transformation)")
    
    print(f"\n3. 📈 Offsets are separate:")
    print(f"   - QUBO offset: {qubo_offset} (from original expression)")
    print(f"   - Ising offset: {ising_offset} (from conversion)")
    print(f"   - Total energy = raw_energy + appropriate_offset")
    
    print(f"\n4. ⚙️  In practice:")
    print(f"   - Optimization algorithms work with raw energies")
    print(f"   - Offsets don't affect which solution is optimal")
    print(f"   - Both formulations find the same optimal solutions")


def demonstrate_wrong_conversion():
    """
    Show what happens with incorrect conversion (for contrast).
    """
    print(f"\n❌ WHAT HAPPENS WITH WRONG CONVERSION?")
    print("=" * 60)
    
    clear_symbol_registry()
    x, y, z = symbols('x y z')
    expr = (x + y + z - 2)**2
    
    qubo, qubo_offset = Compile(expr).get_qubo()
    matrix, index_map = qubo
    
    # Simulate wrong conversion (like my original buggy implementation)
    n = matrix.shape[0]
    J_wrong = np.zeros((n, n))
    h_wrong = np.zeros(n)
    
    # Wrong formula (just as an example)
    for i in range(n):
        for j in range(i+1, n):
            if matrix[i, j] != 0:
                J_wrong[i, j] = matrix[i, j] / 4
                J_wrong[j, i] = matrix[i, j] / 4
    
    for i in range(n):
        h_wrong[i] = matrix[i, i] / 2
        for j in range(i+1, n):
            if matrix[i, j] != 0:
                h_wrong[i] += matrix[i, j] / 4
                h_wrong[j] += matrix[i, j] / 4
    
    print(f"With WRONG conversion formula:")
    
    # Test with one solution
    binary_sol = np.array([1, 1, 0])
    ising_sol = 2 * binary_sol - 1
    
    qubo_energy = binary_sol.T @ matrix @ binary_sol
    ising_wrong_energy = ising_sol.T @ J_wrong @ ising_sol + h_wrong.T @ ising_sol
    
    print(f"QUBO energy:       {qubo_energy}")
    print(f"Ising energy:      {ising_wrong_energy}")
    print(f"Match? {abs(qubo_energy - ising_wrong_energy) < 1e-10}")
    print(f"Difference:        {abs(qubo_energy - ising_wrong_energy)}")
    
    print(f"\n💥 This is why the bug occurred!")
    print(f"   - Wrong conversion → Different energies")
    print(f"   - Algorithm optimizes wrong objective")
    print(f"   - Finds suboptimal solutions")


def main():
    """
    Complete demonstration of QUBO vs Ising energy comparison.
    """
    print("🔬 QUBO vs ISING ENERGY COMPARISON")
    print("Understanding when they are the same vs different")
    
    try:
        compare_energies_detailed()
        answer_the_question()
        demonstrate_wrong_conversion()
        
        print(f"\n" + "=" * 60)
        print(f"🎯 FINAL ANSWER")
        print(f"=" * 60)
        print(f"✅ YES - QUBO and Ising energies ARE the same when:")
        print(f"   1. The conversion formula is implemented correctly")
        print(f"   2. We compare the raw energies (without offsets)")
        print(f"   3. The mathematical transformation x = (s+1)/2 is used")
        
        print(f"\n❌ NO - They are DIFFERENT when:")
        print(f"   1. The conversion formula is wrong (like in the original bug)")
        print(f"   2. We incorrectly include/exclude offsets")
        print(f"   3. The transformation is implemented incorrectly")
        
        print(f"\n💡 The key insight:")
        print(f"   The correct conversion GUARANTEES energy equivalence!")
        print(f"   This is a mathematical requirement, not just a nice-to-have.")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")


if __name__ == "__main__":
    main()
