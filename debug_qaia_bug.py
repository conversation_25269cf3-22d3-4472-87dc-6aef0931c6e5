#!/usr/bin/env python3
"""
Debug script to investigate the QAIASampler bug with the test case:
H = (x + y + z - 2)**2

This should find solutions where exactly 2 out of 3 variables are 1.
Expected solutions: {x:1, y:1, z:0}, {x:1, y:0, z:1}, {x:0, y:1, z:1}
All should have energy = 0.
"""

import numpy as np
from tytan import symbols, Compile, clear_symbol_registry
from tytan.qaia_sampler import QAIASampler


def test_original_problem():
    """Test the exact problem case that's failing."""
    print("=== Testing Original Problem ===")
    
    clear_symbol_registry()
    
    # Create the exact test case
    x = symbols('x')
    y = symbols('y') 
    z = symbols('z')
    H = (x + y + z - 2)**2
    
    print(f"Problem: H = (x + y + z - 2)^2")
    print(f"Expected: exactly 2 out of 3 variables should be 1")
    print(f"Expected solutions: {{x:1,y:1,z:0}}, {{x:1,y:0,z:1}}, {{x:0,y:1,z:1}}")
    print(f"Expected energy: 0 for all optimal solutions")
    
    # Compile to QUBO
    qubo, offset = Compile(H).get_qubo()
    matrix, index_map = qubo
    
    print(f"\nQUBO compilation:")
    print(f"Matrix:\n{matrix}")
    print(f"Index map: {index_map}")
    print(f"Offset: {offset}")
    
    # Test with QAIASampler
    print(f"\n--- Testing QAIASampler ---")
    sampler = QAIASampler(algorithm='asb', seed=42, backend='cpu-float32')
    result = sampler.run(qubo, shots=20, n_iter=100, dt=0.1, xi=0.1)
    
    print(f"QAIASampler results:")
    print(f"Found {len(result)} unique solutions")
    
    for i, (solution, energy, count) in enumerate(result[:5]):  # Show top 5
        print(f"  Solution {i+1}: {solution}, energy={energy}, count={count}")
        
        # Check constraint
        total = sum(solution.values())
        constraint_satisfied = (total == 2)
        print(f"    Constraint (sum=2): {total} {'✓' if constraint_satisfied else '✗'}")
        
        # Check if energy is correct
        expected_energy = (total - 2)**2
        energy_correct = abs(energy - expected_energy) < 1e-6
        print(f"    Energy check: expected={(total-2)**2}, got={energy} {'✓' if energy_correct else '✗'}")
    
    return result


def test_manual_verification():
    """Manually verify the expected solutions."""
    print("\n\n=== Manual Verification ===")
    
    clear_symbol_registry()
    x = symbols('x')
    y = symbols('y')
    z = symbols('z')
    H = (x + y + z - 2)**2
    
    qubo, offset = Compile(H).get_qubo()
    matrix, index_map = qubo
    
    # Expected optimal solutions
    expected_solutions = [
        {'x': 1, 'y': 1, 'z': 0},  # sum = 2
        {'x': 1, 'y': 0, 'z': 1},  # sum = 2  
        {'x': 0, 'y': 1, 'z': 1},  # sum = 2
    ]
    
    print("Manually checking expected solutions:")
    
    for i, sol in enumerate(expected_solutions):
        print(f"\nSolution {i+1}: {sol}")
        
        # Convert to vector form for energy calculation
        sol_vector = np.array([sol[var] for var in sorted(sol.keys())])
        
        # Calculate energy using QUBO matrix
        energy = sol_vector.T @ matrix @ sol_vector
        print(f"  QUBO energy: {energy}")
        
        # Calculate energy using original expression
        constraint_value = sum(sol.values()) - 2
        expected_energy = constraint_value**2
        print(f"  Expected energy: {expected_energy}")
        
        # Verify
        print(f"  Energy correct: {'✓' if abs(energy - expected_energy) < 1e-6 else '✗'}")


def test_qaia_internal_conversion():
    """Test the internal QUBO to Ising conversion."""
    print("\n\n=== Testing QAIA Internal Conversion ===")
    
    clear_symbol_registry()
    x = symbols('x')
    y = symbols('y')
    z = symbols('z')
    H = (x + y + z - 2)**2
    
    qubo, offset = Compile(H).get_qubo()
    matrix, index_map = qubo
    
    # Test the internal conversion
    sampler = QAIASampler(algorithm='asb', backend='cpu-float32')
    J, h, ising_index_map, ising_offset = sampler._qubo_to_ising(matrix, index_map)
    
    print(f"QUBO to Ising conversion:")
    print(f"Original QUBO matrix:\n{matrix}")
    print(f"Ising J matrix:\n{J}")
    print(f"Ising h vector: {h}")
    print(f"Ising offset: {ising_offset}")
    
    # Test conversion back for expected solutions
    print(f"\nTesting conversion for expected solutions:")
    
    expected_binary_solutions = [
        np.array([1, 1, 0]),  # x=1, y=1, z=0
        np.array([1, 0, 1]),  # x=1, y=0, z=1
        np.array([0, 1, 1]),  # x=0, y=1, z=1
    ]
    
    for i, binary_sol in enumerate(expected_binary_solutions):
        print(f"\nSolution {i+1}: {binary_sol}")
        
        # Convert to Ising
        ising_sol = 2 * binary_sol - 1
        print(f"  Ising equivalent: {ising_sol}")
        
        # Calculate Ising energy
        ising_energy = ising_sol.T @ J @ ising_sol + h.T @ ising_sol + ising_offset
        print(f"  Ising energy: {ising_energy}")
        
        # Calculate QUBO energy
        qubo_energy = binary_sol.T @ matrix @ binary_sol
        print(f"  QUBO energy: {qubo_energy}")
        
        # They should be equal
        print(f"  Energies match: {'✓' if abs(ising_energy - qubo_energy) < 1e-6 else '✗'}")


def test_other_samplers():
    """Test with other samplers for comparison."""
    print("\n\n=== Testing Other Samplers for Comparison ===")
    
    clear_symbol_registry()
    x = symbols('x')
    y = symbols('y')
    z = symbols('z')
    H = (x + y + z - 2)**2
    
    qubo, offset = Compile(H).get_qubo()
    
    # Test with SA sampler if available
    try:
        from tytan.sampler import SASampler
        print("Testing SASampler:")
        sa_sampler = SASampler()
        sa_result = sa_sampler.run(qubo, shots=20)
        
        print(f"SASampler found {len(sa_result)} solutions")
        for i, (solution, energy, count) in enumerate(sa_result[:3]):
            total = sum(solution.values())
            print(f"  Solution {i+1}: {solution}, energy={energy}, sum={total}")
            
    except ImportError:
        print("SASampler not available")
    
    # Test with GA sampler if available
    try:
        from tytan.sampler import GASampler
        print("\nTesting GASampler:")
        ga_sampler = GASampler()
        ga_result = ga_sampler.run(qubo, shots=20)
        
        print(f"GASampler found {len(ga_result)} solutions")
        for i, (solution, energy, count) in enumerate(ga_result[:3]):
            total = sum(solution.values())
            print(f"  Solution {i+1}: {solution}, energy={energy}, sum={total}")
            
    except ImportError:
        print("GASampler not available")


def main():
    """Run all debug tests."""
    print("🐛 QAIASampler Bug Investigation")
    print("=" * 60)
    print("Problem: H = (x + y + z - 2)^2")
    print("Expected: Find solutions where exactly 2 variables are 1")
    print("=" * 60)
    
    try:
        # Test the original problem
        qaia_result = test_original_problem()
        
        # Manual verification
        test_manual_verification()
        
        # Test internal conversion
        test_qaia_internal_conversion()
        
        # Test other samplers
        test_other_samplers()
        
        print("\n" + "=" * 60)
        print("🔍 ANALYSIS SUMMARY")
        print("=" * 60)
        
        # Analyze if QAIASampler found correct solutions
        correct_solutions = 0
        for solution, energy, count in qaia_result:
            total = sum(solution.values())
            if total == 2 and abs(energy) < 1e-6:
                correct_solutions += 1
        
        print(f"QAIASampler found {correct_solutions} correct solutions out of {len(qaia_result)} total")
        
        if correct_solutions == 0:
            print("❌ BUG CONFIRMED: QAIASampler is not finding the correct solutions")
            print("   Expected: solutions with sum=2 and energy=0")
            print("   Need to investigate the QUBO-to-Ising conversion or algorithm parameters")
        else:
            print(f"✅ QAIASampler found {correct_solutions} correct solutions")
            if correct_solutions < 3:
                print("⚠️  Not all optimal solutions found, may need parameter tuning")
        
    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
