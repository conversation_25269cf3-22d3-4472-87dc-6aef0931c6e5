#!/usr/bin/env python3
"""
Test script for the new Tytan QAIA modeling function.

This script demonstrates the usage of the use_tytan_qaia function
which provides an alternative QUBO modeling approach using the Tytan library
and solves it with the QAIA algorithm.
"""

import numpy as np
import time
from functions import use_tytan_qaia, make_C

def create_test_data():
    """Create test data for demonstration."""
    # Sample junction information
    junc_info = {
        'junction1': {'num_modes': 3},
        'junction2': {'num_modes': 3},
        'junction3': {'num_modes': 2}
    }
    
    # Sample vehicle counts (C matrix)
    vehicle_counts = {
        'junction1': {'phase0': 10, 'phase1': 15, 'phase2': 8},
        'junction2': {'phase0': 12, 'phase1': 9, 'phase2': 14},
        'junction3': {'phase0': 7, 'phase1': 11}
    }
    
    # Create C matrix using existing function
    C = make_C(vehicle_counts, junc_info)
    
    # Sample binary relationship matrix (BR)
    # This represents conflicts between different junction modes
    BR = {}
    
    # Add some sample conflicts between junctions
    for i in junc_info.keys():
        for j in junc_info.keys():
            if i != j:
                for u in range(junc_info[i]['num_modes']):
                    for v in range(junc_info[j]['num_modes']):
                        # Random conflict values for demonstration
                        BR[i, u, j, v] = np.random.randint(0, 5)
    
    return C, BR, junc_info

def test_tytan_qaia_basic():
    """Test basic functionality of the Tytan QAIA function."""
    print("=== Testing Tytan QAIA Basic Functionality ===")
    
    # Create test data
    C, BR, junc_info = create_test_data()
    
    # Parameters
    alpha = 1.0  # Weight for vehicle throughput
    beta = 0.5   # Weight for conflict minimization
    gamma = 10.0 # Weight for constraints
    num_reads = 20
    
    print(f"Junction info: {junc_info}")
    print(f"C matrix keys: {list(C.keys())}")
    print(f"BR matrix size: {len(BR)}")
    print(f"Parameters: alpha={alpha}, beta={beta}, gamma={gamma}")
    
    try:
        # Test with ASB algorithm
        print("\n--- Testing ASB Algorithm ---")
        solution, result, elapsed_time = use_tytan_qaia(
            C=C,
            BR=BR,
            alpha=alpha,
            beta=beta,
            gamma=gamma,
            num_reads=num_reads,
            algorithm='asb',
            junc_info=junc_info,
            backend='cpu-float32',
            n_iter=100,
            dt=0.1,
            xi=0.1
        )
        
        print(f"Solution: {solution}")
        print(f"Elapsed time: {elapsed_time:.4f} seconds")
        print(f"Best energy: {result[0][1]}")
        
        # Verify constraint satisfaction
        print("\n--- Constraint Verification ---")
        for junc in junc_info.keys():
            if junc in solution:
                total_selected = sum(solution[junc].values())
                print(f"{junc}: {solution[junc]} (sum={total_selected})")
                assert total_selected == 1, f"Constraint violated for {junc}"
        
        print("✓ All constraints satisfied!")
        
    except Exception as e:
        print(f"Error during ASB test: {e}")
        import traceback
        traceback.print_exc()

def test_different_algorithms():
    """Test different QAIA algorithms."""
    print("\n=== Testing Different QAIA Algorithms ===")
    
    # Create test data
    C, BR, junc_info = create_test_data()
    
    # Parameters
    alpha = 1.0
    beta = 0.5
    gamma = 10.0
    num_reads = 15
    
    algorithms = ['asb', 'bsb', 'dsb']
    results = {}
    
    for algorithm in algorithms:
        print(f"\n--- Testing {algorithm.upper()} ---")
        try:
            solution, result, elapsed_time = use_tytan_qaia(
                C=C,
                BR=BR,
                alpha=alpha,
                beta=beta,
                gamma=gamma,
                num_reads=num_reads,
                algorithm=algorithm,
                junc_info=junc_info,
                backend='cpu-float32',
                n_iter=80,
                dt=0.1,
                xi=0.1
            )
            
            results[algorithm] = {
                'solution': solution,
                'energy': result[0][1],
                'time': elapsed_time
            }
            
            print(f"Best energy: {result[0][1]:.4f}")
            print(f"Time: {elapsed_time:.4f}s")
            
        except Exception as e:
            print(f"Error with {algorithm}: {e}")
            results[algorithm] = {'error': str(e)}
    
    # Compare results
    print("\n--- Algorithm Comparison ---")
    for algo, data in results.items():
        if 'error' not in data:
            print(f"{algo.upper()}: Energy={data['energy']:.4f}, Time={data['time']:.4f}s")
        else:
            print(f"{algo.upper()}: Failed - {data['error']}")

def test_parameter_sensitivity():
    """Test sensitivity to different parameters."""
    print("\n=== Testing Parameter Sensitivity ===")
    
    # Create test data
    C, BR, junc_info = create_test_data()
    
    # Test different parameter combinations
    param_sets = [
        {'alpha': 1.0, 'beta': 0.1, 'gamma': 5.0},
        {'alpha': 2.0, 'beta': 0.5, 'gamma': 10.0},
        {'alpha': 0.5, 'beta': 1.0, 'gamma': 15.0}
    ]
    
    for i, params in enumerate(param_sets):
        print(f"\n--- Parameter Set {i+1}: {params} ---")
        try:
            solution, result, elapsed_time = use_tytan_qaia(
                C=C,
                BR=BR,
                alpha=params['alpha'],
                beta=params['beta'],
                gamma=params['gamma'],
                num_reads=10,
                algorithm='asb',
                junc_info=junc_info,
                n_iter=50
            )
            
            print(f"Best energy: {result[0][1]:.4f}")
            print(f"Solution: {solution}")
            
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    print("Testing Tytan QAIA Modeling Function")
    print("=" * 50)
    
    # Set random seed for reproducibility
    np.random.seed(42)
    
    # Run tests
    test_tytan_qaia_basic()
    test_different_algorithms()
    test_parameter_sensitivity()
    
    print("\n" + "=" * 50)
    print("Testing completed!")
