#!/usr/bin/env python3
"""
Demonstration of the QAIASampler class.

This script shows how to use the QAIASampler to solve QUBO problems
using the QAIA (Quantum Annealing Inspired Algorithm) algorithms.
"""

import numpy as np
from tytan import symbols, Compile
from tytan.qaia_sampler import QAIASampler


def demo_simple_constraint():
    """Demonstrate solving a simple constraint satisfaction problem."""
    print("=== Simple Constraint Problem ===")
    print("Problem: Find x, y such that x + y = 1 (exactly one should be 1)")

    # Create variables
    x, y = symbols('x y')

    # Constraint: (x + y - 1)^2 should be minimized (penalty for not summing to 1)
    expr = (x + y - 1)**2

    # Compile to QUBO
    qubo, offset = Compile(expr).get_qubo()
    print(f"QUBO matrix:\n{qubo[0]}")
    print(f"Variables: {list(qubo[1].keys())}")
    print(f"Offset: {offset}")

    # Solve with different QAIA algorithms
    algorithms = ['asb', 'bsb', 'dsb']

    for algorithm in algorithms:
        print(f"\n--- Using {algorithm.upper()} algorithm ---")
        sampler = QAIASampler(algorithm=algorithm, seed=42, backend='cpu-float32')
        result = sampler.run(qubo, shots=10, n_iter=100)

        print(f"Best solution: {result[0][0]}")
        print(f"Energy: {result[0][1]}")
        print(f"Count: {result[0][2]}")

        # Verify the constraint
        x_val, y_val = result[0][0]['x'], result[0][0]['y']
        constraint_value = x_val + y_val
        print(f"Constraint check (x + y): {constraint_value} (should be 1)")


def demo_max_cut_problem():
    """Demonstrate solving a Max-Cut style problem."""
    print("\n\n=== Max-Cut Style Problem ===")
    print("Problem: Partition 4 nodes to maximize cut (minimize agreements)")

    # Create 4 binary variables representing node assignments
    x1, x2, x3, x4 = symbols('x1 x2 x3 x4')

    # Max-cut objective: minimize agreements between connected nodes
    # Graph edges: (1,2), (2,3), (3,4), (4,1), (1,3)
    # For each edge (i,j), add penalty for xi == xj
    # This is equivalent to minimizing: sum of xi*xj + (1-xi)*(1-xj) for each edge
    # Which simplifies to: sum of (2*xi*xj - xi - xj + 1) for each edge

    edges = [(x1, x2), (x2, x3), (x3, x4), (x4, x1), (x1, x3)]
    expr = sum(2*xi*xj - xi - xj + 1 for xi, xj in edges)

    print("Graph edges: (1,2), (2,3), (3,4), (4,1), (1,3)")
    print("Objective: minimize agreements between connected nodes")

    # Compile to QUBO
    qubo, offset = Compile(expr).get_qubo()

    # Solve with ASB algorithm
    sampler = QAIASampler(algorithm='asb', seed=42, backend='cpu-float32')
    result = sampler.run(qubo, shots=20, n_iter=200)

    print(f"\nBest solution: {result[0][0]}")
    print(f"Energy: {result[0][1]}")

    # Calculate and display the cut value
    solution = result[0][0]
    cut_edges = 0
    total_edges = len(edges)

    for i, (var1, var2) in enumerate(edges):
        var1_name = str(var1)
        var2_name = str(var2)
        val1 = solution[var1_name]
        val2 = solution[var2_name]

        if val1 != val2:  # Different partitions = edge is cut
            cut_edges += 1

        print(f"Edge {var1_name}-{var2_name}: {val1}-{val2} ({'cut' if val1 != val2 else 'not cut'})")

    print(f"\nCut edges: {cut_edges}/{total_edges}")
    print(f"Cut ratio: {cut_edges/total_edges:.2%}")


def demo_knapsack_problem():
    """Demonstrate solving a simple knapsack problem."""
    print("\n\n=== Simple Knapsack Problem ===")
    print("Problem: Select items to maximize value while staying within weight limit")

    # Items: (value, weight)
    items = [
        (10, 5),   # item 1: value=10, weight=5
        (40, 4),   # item 2: value=40, weight=4
        (30, 6),   # item 3: value=30, weight=6
        (50, 8),   # item 4: value=50, weight=8
    ]
    capacity = 10

    print(f"Items (value, weight): {items}")
    print(f"Knapsack capacity: {capacity}")

    # Create binary variables for each item
    x = [symbols(f'x{i+1}') for i in range(len(items))]

    # Objective: maximize value = minimize negative value
    value_term = -sum(items[i][0] * x[i] for i in range(len(items)))

    # Constraint: weight <= capacity
    # Penalty for exceeding capacity: (sum of weights - capacity)^2 when positive
    weight_sum = sum(items[i][1] * x[i] for i in range(len(items)))
    weight_penalty = 1000 * (weight_sum - capacity)**2  # Large penalty coefficient

    # Combined objective
    expr = value_term + weight_penalty

    # Compile to QUBO
    qubo, offset = Compile(expr).get_qubo()

    # Solve with BSB algorithm
    sampler = QAIASampler(algorithm='bsb', seed=42, backend='cpu-float32')
    result = sampler.run(qubo, shots=30, n_iter=300)

    print(f"\nBest solution: {result[0][0]}")
    print(f"Energy: {result[0][1]}")

    # Analyze the solution
    solution = result[0][0]
    total_value = 0
    total_weight = 0
    selected_items = []

    for i in range(len(items)):
        var_name = f'x{i+1}'
        if solution[var_name] == 1:
            selected_items.append(i+1)
            total_value += items[i][0]
            total_weight += items[i][1]

    print(f"\nSelected items: {selected_items}")
    print(f"Total value: {total_value}")
    print(f"Total weight: {total_weight} (capacity: {capacity})")
    print(f"Weight constraint satisfied: {total_weight <= capacity}")


def demo_performance_comparison():
    """Compare performance of different QAIA algorithms."""
    print("\n\n=== Performance Comparison ===")
    print("Comparing ASB, BSB, and DSB on the same problem")

    # Create a simpler problem to avoid overflow
    x1, x2, x3 = symbols('x1 x2 x3')

    # Simple constraint problem: exactly 2 out of 3 should be selected
    expr = (x1 + x2 + x3 - 2)**2

    qubo, offset = Compile(expr).get_qubo()

    algorithms = ['asb', 'bsb', 'dsb']
    results = {}

    for algorithm in algorithms:
        print(f"\nTesting {algorithm.upper()}...")
        try:
            sampler = QAIASampler(algorithm=algorithm, seed=123, backend='cpu-float32')
            result = sampler.run(qubo, shots=20, n_iter=100, dt=0.1, xi=0.1)

            results[algorithm] = {
                'best_energy': result[0][1],
                'best_solution': result[0][0],
                'unique_solutions': len(result)
            }

            print(f"  Best energy: {result[0][1]:.6f}")
            print(f"  Unique solutions found: {len(result)}")
            print(f"  Best solution: {result[0][0]}")

        except Exception as e:
            print(f"  ❌ Failed: {e}")
            results[algorithm] = {'best_energy': float('inf')}

    # Find the best overall result
    valid_results = {k: v for k, v in results.items() if v['best_energy'] != float('inf')}
    if valid_results:
        best_algorithm = min(valid_results.keys(), key=lambda k: valid_results[k]['best_energy'])
        print(f"\nBest performing algorithm: {best_algorithm.upper()}")
        print(f"Best energy achieved: {valid_results[best_algorithm]['best_energy']:.6f}")
    else:
        print("\nNo algorithms completed successfully.")


def main():
    """Run all demonstrations."""
    print("🚀 QAIASampler Demonstration")
    print("=" * 50)

    try:
        demo_simple_constraint()
        demo_max_cut_problem()
        demo_knapsack_problem()
        demo_performance_comparison()

        print("\n" + "=" * 50)
        print("✅ All demonstrations completed successfully!")
        print("\nThe QAIASampler provides:")
        print("- Automatic QUBO to Ising conversion")
        print("- Access to ASB, BSB, and DSB algorithms")
        print("- Consistent output format with other samplers")
        print("- Proper binary solution extraction using torch.sign()")

    except Exception as e:
        print(f"\n❌ Error during demonstration: {e}")
        print("Make sure all dependencies are installed and the conda environment is activated.")


if __name__ == "__main__":
    main()
