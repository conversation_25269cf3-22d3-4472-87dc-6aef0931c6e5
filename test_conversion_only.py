#!/usr/bin/env python3
"""
Test only the QUBO to Ising conversion.
"""

import sys
sys.path.insert(0, '/Users/<USER>/Documents/snow/tytan-main')

from tytan import symbols, Compile, clear_symbol_registry
from tytan.qaia_sampler import QAIASampler
import numpy as np


def main():
    print("Testing QUBO to Ising conversion...")
    
    clear_symbol_registry()
    x, y, z = symbols('x y z')
    H = (x + y + z - 2)**2
    
    qubo, offset = Compile(H).get_qubo()
    matrix, index_map = qubo
    
    print("QUBO matrix:")
    print(matrix)
    print("Index map:", index_map)
    print("Offset:", offset)
    
    # Test conversion
    sampler = QAIASampler()
    J, h, ising_index_map, ising_offset = sampler._qubo_to_ising(matrix, index_map)
    
    print("\nConverted Ising parameters:")
    print("J matrix:")
    print(J)
    print("h vector:", h)
    print("offset:", ising_offset)
    
    # Test with expected solution [1,1,0]
    binary_sol = np.array([1, 1, 0])
    qubo_energy = binary_sol.T @ matrix @ binary_sol
    ising_sol = 2 * binary_sol - 1
    ising_energy = ising_sol.T @ J @ ising_sol + h.T @ ising_sol + ising_offset
    
    print(f"\nTesting solution [1,1,0]:")
    print(f"QUBO energy: {qubo_energy}")
    print(f"Ising energy: {ising_energy}")
    print(f"Match: {abs(qubo_energy - ising_energy) < 1e-10}")
    
    if abs(qubo_energy - ising_energy) < 1e-10:
        print("✅ Conversion is correct!")
    else:
        print("❌ Conversion has errors")


if __name__ == "__main__":
    main()
