import numpy as np
import itertools
import inspect
from symengine import symbols as symengine_symbols

# Global registry to track symbol types
_symbol_type_registry = {}

"""
SympyのSymbol関数にそのまま投げる関数
importをTYTANだけにするための申し訳ない方策
Enhanced to support symbol type declaration
"""
def symbols(passed_txt, symbol_type="binary"):
    """
    Create symbols with specified type.

    Args:
        passed_txt (str): Symbol names (space-separated for multiple symbols)
        symbol_type (str): Type of symbols - "binary" (0,1) or "ising" (-1,+1)

    Returns:
        Symbol or tuple of symbols
    """
    if symbol_type not in ["binary", "ising"]:
        raise TytanException("symbol_type must be 'binary' or 'ising'")

    result = symengine_symbols(passed_txt)

    # Register symbol types
    if isinstance(result, (list, tuple)):
        for sym in result:
            _symbol_type_registry[str(sym)] = symbol_type
    else:
        _symbol_type_registry[str(result)] = symbol_type

    return result

class TytanException(Exception):
    pass

"""
リストでまとめて定義する関数
Enhanced to support symbol type declaration
"""
def symbols_list(shape, format_txt, symbol_type="binary"):
    """
    Create a list/array of symbols with specified type.

    Args:
        shape: Shape of the symbol array
        format_txt (str): Format string for symbol names
        symbol_type (str): Type of symbols - "binary" (0,1) or "ising" (-1,+1)

    Returns:
        numpy array of symbols
    """
    #単一intの場合
    if type(shape) == int:
        shape = [shape]
    #print(shape)

    #次元チェック
    dim = len(shape)
    if dim != format_txt.count('{}'):
        raise TytanException("specify format option like format_txt=\'q{}_{}\' as dimension")

    #{}のセパレートチェック
    if '}{' in format_txt:
        raise TytanException("separate {} in format_txt like format_txt=\'q{}_{}\'")

    #次元が1～5でなければエラー
    if dim not in [1, 2, 3, 4, 5]:
        raise TytanException("Currently only dim<=5 is available. Ask tytan community.")

    #再帰的にシンボルを作成する
    def recursive_create(indices):
        if len(indices) == dim:
            return symbols(format_txt.format(*indices), symbol_type=symbol_type)
        else:
            return [recursive_create(indices + [i]) for i in range(shape[len(indices)])]
    q = recursive_create([])

    return np.array(q)



"""
個別定義用のコマンドを返す関数
exec(command)して定義
Enhanced to support symbol type declaration
"""
def symbols_define(shape, format_txt, symbol_type="binary"):
    """
    Generate command strings to define symbols with specified type.

    Args:
        shape: Shape of the symbol array
        format_txt (str): Format string for symbol names
        symbol_type (str): Type of symbols - "binary" (0,1) or "ising" (-1,+1)

    Returns:
        String of commands to execute
    """
    #単一intの場合
    if type(shape) == int:
        shape = [shape]
    #print(shape)

    #次元チェック
    dim = len(shape)
    if dim != format_txt.count('{}'):
        raise TytanException("specify format option like format_txt=\'q{}_{}\' as dimension")

    #{}のセパレートチェック
    if '}{' in format_txt:
        raise TytanException("separate {} in format_txt like format_txt=\'q{}_{}\'")

    #次元が1～5でなければエラー
    if dim not in [1, 2, 3, 4, 5]:
        raise TytanException("Currently only dim<=5 is available. Ask tytan community.")

    #再帰的に定義を作成する
    # For backward compatibility, only include symbol_type if it's not the default "binary"
    if symbol_type == "binary":
        command = f"{format_txt} = symbols('{format_txt}')"
    else:
        command = f"{format_txt} = symbols('{format_txt}', symbol_type='{symbol_type}')"

    def recursive_create(indices):
        if len(indices) == dim:
            return command.format(*indices, *indices) + "\r\n"
        else:
            return "".join(recursive_create(indices + [i]) for i in range(shape[len(indices)]))
    ret = recursive_create([])

    return ret[:-2]

    # #表示用
    # start_indices = [0] * dim
    # end_indices = [s - 1 for s in shape]
    # first_command = command.format(*start_indices, *start_indices)
    # final_command = command.format(*end_indices, *end_indices)
    # print(f'defined global: {first_command} to {final_command}')


def symbols_nbit(start, stop, format_txt, num=8, symbol_type="binary"):
    """
    Create N-bit representation symbols with specified type.

    Args:
        start: Start value of the range
        stop: Stop value of the range
        format_txt (str): Format string for symbol names
        num (int): Number of bits
        symbol_type (str): Type of symbols - "binary" (0,1) or "ising" (-1,+1)

    Returns:
        Expression representing the N-bit value
    """
    #次元チェック
    if 1 != format_txt.count('{}'):
        raise TytanException("specify format option like format_txt=\'q{}\' and should be one dimension.")

    #生成
    q = symbols_list(num, format_txt=format_txt, symbol_type=symbol_type)

    #式
    ret = 0
    for n in range(num):
        #係数を規格化してから量子ビットをかけたい
        ret += (start + (stop - start)) * 2**(num - n - 1) / 2**num * q[n]

    return ret


def get_symbol_type(symbol):
    """
    Get the type of a symbol.

    Args:
        symbol: Symbol to check

    Returns:
        str: "binary", "ising", or "unknown"
    """
    symbol_str = str(symbol)
    return _symbol_type_registry.get(symbol_str, "unknown")


def set_symbol_type(symbol, symbol_type):
    """
    Set the type of a symbol.

    Args:
        symbol: Symbol to set type for
        symbol_type (str): Type - "binary" or "ising"
    """
    if symbol_type not in ["binary", "ising"]:
        raise TytanException("symbol_type must be 'binary' or 'ising'")

    symbol_str = str(symbol)
    _symbol_type_registry[symbol_str] = symbol_type


def get_all_symbol_types():
    """
    Get all registered symbol types.

    Returns:
        dict: Dictionary mapping symbol names to types
    """
    return _symbol_type_registry.copy()


def clear_symbol_registry():
    """
    Clear the symbol type registry.
    """
    global _symbol_type_registry
    _symbol_type_registry = {}
